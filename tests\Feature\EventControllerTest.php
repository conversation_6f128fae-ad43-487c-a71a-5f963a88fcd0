<?php

namespace Tests\Feature;

use App\Models\Calendar;
use App\Models\Category;
use App\Models\Event;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EventControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Calendar $calendar;
    private Category $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->calendar = Calendar::factory()->create(['user_id' => $this->user->id]);
        $this->category = Category::factory()->create(['user_id' => $this->user->id]);
    }

    public function test_user_can_view_events_index()
    {
        $this->actingAs($this->user)
            ->get('/events')
            ->assertStatus(200)
            ->assertInertia(fn ($page) => $page->component('Events/Index'));
    }

    public function test_user_can_create_event()
    {
        $eventData = [
            'title' => 'Test Event',
            'description' => 'Test Description',
            'start_date' => now()->addDay()->format('Y-m-d H:i'),
            'end_date' => now()->addDay()->addHour()->format('Y-m-d H:i'),
            'all_day' => false,
            'location' => 'Test Location',
            'status' => 'confirmed',
            'priority' => 'medium',
            'calendar_id' => $this->calendar->id,
            'category_id' => $this->category->id,
            'recurrence_type' => 'none',
            'recurrence_interval' => 1,
        ];

        $this->actingAs($this->user)
            ->post('/events', $eventData)
            ->assertRedirect('/calendar');

        $this->assertDatabaseHas('events', [
            'title' => 'Test Event',
            'user_id' => $this->user->id,
            'calendar_id' => $this->calendar->id,
        ]);
    }

    public function test_user_can_update_event()
    {
        $event = Event::factory()->create([
            'user_id' => $this->user->id,
            'calendar_id' => $this->calendar->id,
        ]);

        $updateData = [
            'title' => 'Updated Event',
            'description' => 'Updated Description',
            'start_date' => $event->start_date->format('Y-m-d H:i'),
            'end_date' => $event->end_date->format('Y-m-d H:i'),
            'all_day' => false,
            'location' => 'Updated Location',
            'status' => 'confirmed',
            'priority' => 'high',
            'calendar_id' => $this->calendar->id,
            'category_id' => $this->category->id,
            'recurrence_type' => 'none',
            'recurrence_interval' => 1,
        ];

        $this->actingAs($this->user)
            ->put("/events/{$event->id}", $updateData)
            ->assertRedirect('/calendar');

        $this->assertDatabaseHas('events', [
            'id' => $event->id,
            'title' => 'Updated Event',
            'priority' => 'high',
        ]);
    }

    public function test_user_can_delete_event()
    {
        $event = Event::factory()->create([
            'user_id' => $this->user->id,
            'calendar_id' => $this->calendar->id,
        ]);

        $this->actingAs($this->user)
            ->delete("/events/{$event->id}")
            ->assertRedirect('/calendar');

        $this->assertDatabaseMissing('events', [
            'id' => $event->id,
        ]);
    }

    public function test_user_cannot_access_other_users_events()
    {
        $otherUser = User::factory()->create();
        $otherCalendar = Calendar::factory()->create(['user_id' => $otherUser->id]);
        $otherEvent = Event::factory()->create([
            'user_id' => $otherUser->id,
            'calendar_id' => $otherCalendar->id,
        ]);

        $this->actingAs($this->user)
            ->get("/events/{$otherEvent->id}")
            ->assertStatus(403);

        $this->actingAs($this->user)
            ->put("/events/{$otherEvent->id}", [
                'title' => 'Hacked Event',
                'start_date' => now()->format('Y-m-d H:i'),
                'end_date' => now()->addHour()->format('Y-m-d H:i'),
                'calendar_id' => $this->calendar->id,
            ])
            ->assertStatus(403);
    }

    public function test_api_returns_events_for_date_range()
    {
        $event1 = Event::factory()->create([
            'user_id' => $this->user->id,
            'calendar_id' => $this->calendar->id,
            'start_date' => now()->addDays(5),
            'end_date' => now()->addDays(5)->addHour(),
        ]);

        $event2 = Event::factory()->create([
            'user_id' => $this->user->id,
            'calendar_id' => $this->calendar->id,
            'start_date' => now()->addDays(10),
            'end_date' => now()->addDays(10)->addHour(),
        ]);

        $response = $this->actingAs($this->user)
            ->get('/api/events?' . http_build_query([
                'start' => now()->addDays(4)->format('Y-m-d'),
                'end' => now()->addDays(6)->format('Y-m-d'),
            ]))
            ->assertStatus(200);

        $events = $response->json();
        $this->assertCount(1, $events);
        $this->assertEquals($event1->title, $events[0]['title']);
    }

    public function test_event_validation_rules()
    {
        $this->actingAs($this->user)
            ->post('/events', [])
            ->assertSessionHasErrors(['title', 'start_date', 'end_date', 'calendar_id']);

        $this->actingAs($this->user)
            ->post('/events', [
                'title' => '',
                'start_date' => 'invalid-date',
                'end_date' => now()->subHour()->format('Y-m-d H:i'), // End before start
                'calendar_id' => 999, // Non-existent calendar
            ])
            ->assertSessionHasErrors(['title', 'start_date', 'end_date', 'calendar_id']);
    }

    public function test_recurring_event_creation()
    {
        $eventData = [
            'title' => 'Weekly Meeting',
            'description' => 'Team standup',
            'start_date' => now()->addDay()->format('Y-m-d H:i'),
            'end_date' => now()->addDay()->addHour()->format('Y-m-d H:i'),
            'all_day' => false,
            'calendar_id' => $this->calendar->id,
            'recurrence_type' => 'weekly',
            'recurrence_interval' => 1,
            'recurrence_end_date' => now()->addMonth()->format('Y-m-d'),
        ];

        $this->actingAs($this->user)
            ->post('/events', $eventData)
            ->assertRedirect('/calendar');

        $this->assertDatabaseHas('events', [
            'title' => 'Weekly Meeting',
            'recurrence_type' => 'weekly',
            'recurrence_interval' => 1,
        ]);
    }
}
