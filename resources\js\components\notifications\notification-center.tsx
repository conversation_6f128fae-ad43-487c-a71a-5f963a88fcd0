import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useNotifications } from '@/lib/notifications';
import { Bell, Check, CheckCheck, Trash2, X } from 'lucide-react';

export function NotificationCenter() {
    const {
        notifications,
        unreadCount,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        clearAll,
        requestPermission,
    } = useNotifications();

    const handleRequestPermission = async () => {
        const granted = await requestPermission();
        if (granted) {
            // You could show a success message here
        }
    };

    const getNotificationIcon = (type: string) => {
        switch (type) {
            case 'success':
                return '✅';
            case 'warning':
                return '⚠️';
            case 'error':
                return '❌';
            default:
                return 'ℹ️';
        }
    };

    const formatTime = (date: Date) => {
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}j`;
        } else if (hours > 0) {
            return `${hours}h`;
        } else if (minutes > 0) {
            return `${minutes}m`;
        } else {
            return 'maintenant';
        }
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="relative">
                    <Bell className="h-4 w-4" />
                    {unreadCount > 0 && (
                        <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">
                            {unreadCount > 9 ? '9+' : unreadCount}
                        </span>
                    )}
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
                <div className="flex items-center justify-between p-2">
                    <h3 className="font-semibold">Notifications</h3>
                    <div className="flex gap-1">
                        {unreadCount > 0 && (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={markAllAsRead}
                                title="Marquer tout comme lu"
                            >
                                <CheckCheck className="h-4 w-4" />
                            </Button>
                        )}
                        {notifications.length > 0 && (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={clearAll}
                                title="Effacer toutes les notifications"
                            >
                                <Trash2 className="h-4 w-4" />
                            </Button>
                        )}
                    </div>
                </div>
                
                <DropdownMenuSeparator />
                
                {notifications.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">
                        <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>Aucune notification</p>
                    </div>
                ) : (
                    <div className="max-h-96 overflow-y-auto">
                        {notifications.slice(0, 10).map((notification) => (
                            <div
                                key={notification.id}
                                className={`p-3 border-b border-border last:border-b-0 ${
                                    !notification.read ? 'bg-muted/50' : ''
                                }`}
                            >
                                <div className="flex items-start gap-2">
                                    <span className="text-sm">
                                        {getNotificationIcon(notification.type)}
                                    </span>
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between">
                                            <h4 className="text-sm font-medium truncate">
                                                {notification.title}
                                            </h4>
                                            <span className="text-xs text-muted-foreground ml-2">
                                                {formatTime(notification.timestamp)}
                                            </span>
                                        </div>
                                        <p className="text-sm text-muted-foreground mt-1">
                                            {notification.message}
                                        </p>
                                        <div className="flex items-center gap-1 mt-2">
                                            {!notification.read && (
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => markAsRead(notification.id)}
                                                    className="h-6 px-2 text-xs"
                                                >
                                                    <Check className="h-3 w-3 mr-1" />
                                                    Marquer comme lu
                                                </Button>
                                            )}
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => deleteNotification(notification.id)}
                                                className="h-6 px-2 text-xs text-red-600 hover:text-red-700"
                                            >
                                                <X className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                        
                        {notifications.length > 10 && (
                            <div className="p-2 text-center text-sm text-muted-foreground">
                                +{notifications.length - 10} autres notifications
                            </div>
                        )}
                    </div>
                )}
                
                <DropdownMenuSeparator />
                
                <div className="p-2">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleRequestPermission}
                        className="w-full text-xs"
                    >
                        Activer les notifications du navigateur
                    </Button>
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
