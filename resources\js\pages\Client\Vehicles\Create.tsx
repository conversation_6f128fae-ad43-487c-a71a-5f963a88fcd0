import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Car } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

export default function CreateVehicle() {
    const { data, setData, post, processing, errors } = useForm({
        brand: '',
        model: '',
        year: new Date().getFullYear(),
        color: '',
        license_plate: '',
        vehicle_type: 'car',
        notes: '',
    });

    const submit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/client/vehicles');
    };

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <Head title="Ajouter un véhicule - AutoWash" />
            
            <div className="max-w-2xl mx-auto space-y-6">
                <div className="flex items-center gap-4">
                    <Button asChild variant="outline" size="sm">
                        <Link href="/client/vehicles">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Retour
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Ajouter un véhicule</h1>
                        <p className="text-gray-600">Enregistrez un nouveau véhicule</p>
                    </div>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Car className="h-5 w-5" />
                            Informations du véhicule
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={submit} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="brand">Marque</Label>
                                    <Input
                                        id="brand"
                                        value={data.brand}
                                        onChange={(e) => setData('brand', e.target.value)}
                                        placeholder="ex: Toyota"
                                        required
                                    />
                                    {errors.brand && <p className="text-sm text-red-600">{errors.brand}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="model">Modèle</Label>
                                    <Input
                                        id="model"
                                        value={data.model}
                                        onChange={(e) => setData('model', e.target.value)}
                                        placeholder="ex: Corolla"
                                        required
                                    />
                                    {errors.model && <p className="text-sm text-red-600">{errors.model}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="year">Année</Label>
                                    <Input
                                        id="year"
                                        type="number"
                                        value={data.year}
                                        onChange={(e) => setData('year', parseInt(e.target.value))}
                                        min="1900"
                                        max={new Date().getFullYear() + 1}
                                        required
                                    />
                                    {errors.year && <p className="text-sm text-red-600">{errors.year}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="color">Couleur</Label>
                                    <Input
                                        id="color"
                                        value={data.color}
                                        onChange={(e) => setData('color', e.target.value)}
                                        placeholder="ex: Rouge"
                                        required
                                    />
                                    {errors.color && <p className="text-sm text-red-600">{errors.color}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="license_plate">Plaque d'immatriculation</Label>
                                    <Input
                                        id="license_plate"
                                        value={data.license_plate}
                                        onChange={(e) => setData('license_plate', e.target.value.toUpperCase())}
                                        placeholder="ex: AB-123-CD"
                                        required
                                    />
                                    {errors.license_plate && <p className="text-sm text-red-600">{errors.license_plate}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="vehicle_type">Type de véhicule</Label>
                                    <Select value={data.vehicle_type} onValueChange={(value) => setData('vehicle_type', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="car">Voiture</SelectItem>
                                            <SelectItem value="suv">SUV</SelectItem>
                                            <SelectItem value="truck">Camion</SelectItem>
                                            <SelectItem value="motorcycle">Moto</SelectItem>
                                            <SelectItem value="van">Fourgon</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.vehicle_type && <p className="text-sm text-red-600">{errors.vehicle_type}</p>}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="notes">Notes (optionnel)</Label>
                                <Textarea
                                    id="notes"
                                    value={data.notes}
                                    onChange={(e) => setData('notes', e.target.value)}
                                    placeholder="Informations supplémentaires..."
                                    rows={3}
                                />
                                {errors.notes && <p className="text-sm text-red-600">{errors.notes}</p>}
                            </div>

                            <div className="flex gap-4">
                                <Button type="submit" disabled={processing} className="flex-1">
                                    {processing ? 'Enregistrement...' : 'Ajouter le véhicule'}
                                </Button>
                                <Button asChild variant="outline" type="button">
                                    <Link href="/client/vehicles">Annuler</Link>
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
