<?php

return [
    /*
    |--------------------------------------------------------------------------
    | SmartCalendar Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the SmartCalendar application.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Event Limits
    |--------------------------------------------------------------------------
    |
    | These options control limits for events to prevent performance issues.
    |
    */
    'events' => [
        'max_per_user' => env('SMARTCALENDAR_MAX_EVENTS_PER_USER', 10000),
        'max_recurrence_instances' => env('SMARTCALENDAR_MAX_RECURRENCE_INSTANCES', 1000),
        'max_date_range_days' => env('SMARTCALENDAR_MAX_DATE_RANGE_DAYS', 365),
    ],

    /*
    |--------------------------------------------------------------------------
    | Calendar Limits
    |--------------------------------------------------------------------------
    |
    | These options control limits for calendars per user.
    |
    */
    'calendars' => [
        'max_per_user' => env('SMARTCALENDAR_MAX_CALENDARS_PER_USER', 50),
    ],

    /*
    |--------------------------------------------------------------------------
    | Category Limits
    |--------------------------------------------------------------------------
    |
    | These options control limits for categories per user.
    |
    */
    'categories' => [
        'max_per_user' => env('SMARTCALENDAR_MAX_CATEGORIES_PER_USER', 100),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for the notification system.
    |
    */
    'notifications' => [
        'enabled' => env('SMARTCALENDAR_NOTIFICATIONS_ENABLED', true),
        'default_reminder_minutes' => env('SMARTCALENDAR_DEFAULT_REMINDER_MINUTES', 15),
        'max_notifications_per_user' => env('SMARTCALENDAR_MAX_NOTIFICATIONS_PER_USER', 100),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for caching to improve performance.
    |
    */
    'cache' => [
        'events_ttl' => env('SMARTCALENDAR_EVENTS_CACHE_TTL', 300), // 5 minutes
        'calendars_ttl' => env('SMARTCALENDAR_CALENDARS_CACHE_TTL', 3600), // 1 hour
        'categories_ttl' => env('SMARTCALENDAR_CATEGORIES_CACHE_TTL', 3600), // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Colors
    |--------------------------------------------------------------------------
    |
    | Default color palette for calendars and categories.
    |
    */
    'colors' => [
        'calendars' => [
            '#3B82F6', // Blue
            '#EF4444', // Red
            '#10B981', // Green
            '#F59E0B', // Yellow
            '#8B5CF6', // Purple
            '#EC4899', // Pink
            '#06B6D4', // Cyan
            '#84CC16', // Lime
            '#F97316', // Orange
            '#6366F1', // Indigo
        ],
        'categories' => [
            '#3B82F6', // Blue
            '#EF4444', // Red
            '#10B981', // Green
            '#F59E0B', // Yellow
            '#8B5CF6', // Purple
            '#EC4899', // Pink
            '#06B6D4', // Cyan
            '#84CC16', // Lime
            '#F97316', // Orange
            '#6366F1', // Indigo
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    |
    | Enable or disable specific features.
    |
    */
    'features' => [
        'recurring_events' => env('SMARTCALENDAR_RECURRING_EVENTS', true),
        'public_calendars' => env('SMARTCALENDAR_PUBLIC_CALENDARS', true),
        'event_categories' => env('SMARTCALENDAR_EVENT_CATEGORIES', true),
        'event_search' => env('SMARTCALENDAR_EVENT_SEARCH', true),
        'calendar_stats' => env('SMARTCALENDAR_CALENDAR_STATS', true),
        'event_export' => env('SMARTCALENDAR_EVENT_EXPORT', false),
        'calendar_sharing' => env('SMARTCALENDAR_CALENDAR_SHARING', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Date and Time Settings
    |--------------------------------------------------------------------------
    |
    | Default settings for date and time handling.
    |
    */
    'datetime' => [
        'default_timezone' => env('SMARTCALENDAR_DEFAULT_TIMEZONE', 'UTC'),
        'date_format' => env('SMARTCALENDAR_DATE_FORMAT', 'Y-m-d'),
        'time_format' => env('SMARTCALENDAR_TIME_FORMAT', 'H:i'),
        'datetime_format' => env('SMARTCALENDAR_DATETIME_FORMAT', 'Y-m-d H:i:s'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Search Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for event search functionality.
    |
    */
    'search' => [
        'max_results' => env('SMARTCALENDAR_SEARCH_MAX_RESULTS', 100),
        'min_query_length' => env('SMARTCALENDAR_SEARCH_MIN_QUERY_LENGTH', 2),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Settings to optimize application performance.
    |
    */
    'performance' => [
        'eager_load_relations' => env('SMARTCALENDAR_EAGER_LOAD_RELATIONS', true),
        'use_database_indexes' => env('SMARTCALENDAR_USE_DATABASE_INDEXES', true),
        'paginate_large_results' => env('SMARTCALENDAR_PAGINATE_LARGE_RESULTS', true),
        'pagination_size' => env('SMARTCALENDAR_PAGINATION_SIZE', 50),
    ],
];
