@echo off
echo Setting up SmartCalendar application...
echo.

echo Installing PHP dependencies...
call composer install
if %errorlevel% neq 0 (
    echo Error installing PHP dependencies
    pause
    exit /b 1
)

echo.
echo Installing Node.js dependencies...
call npm install
if %errorlevel% neq 0 (
    echo Error installing Node.js dependencies
    pause
    exit /b 1
)

echo.
echo Setting up environment file...
if not exist .env (
    copy .env.example .env
    echo Environment file created
) else (
    echo Environment file already exists
)

echo.
echo Generating application key...
call php artisan key:generate
if %errorlevel% neq 0 (
    echo Error generating application key
    pause
    exit /b 1
)

echo.
echo Running database migrations...
call php artisan migrate:fresh
if %errorlevel% neq 0 (
    echo Error running migrations
    pause
    exit /b 1
)

echo.
echo Seeding database with sample data...
call php artisan db:seed
if %errorlevel% neq 0 (
    echo Error seeding database
    pause
    exit /b 1
)

echo.
echo Building frontend assets...
call npm run build
if %errorlevel% neq 0 (
    echo Error building frontend assets
    pause
    exit /b 1
)

echo.
echo ========================================
echo SmartCalendar setup completed successfully!
echo ========================================
echo.
echo You can now start the development server with:
echo   php artisan serve
echo.
echo And in another terminal, start the frontend development server with:
echo   npm run dev
echo.
echo Default login credentials:
echo   Email: <EMAIL>
echo   Password: password
echo.
pause
