import { Head, Link, router } from '@inertiajs/react';
import { useState } from 'react';
import { Car, Search, Eye, User, Calendar, Filter } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';

interface Vehicle {
    id: number;
    brand: string;
    model: string;
    year: number;
    color: string;
    license_plate: string;
    vehicle_type: string;
    created_at: string;
    appointments_count: number;
    user: {
        id: number;
        name: string;
        email: string;
    };
}

interface VehiclesIndexProps {
    vehicles: {
        data: Vehicle[];
        links: any[];
        meta: any;
    };
    filters: {
        search?: string;
        type?: string;
    };
}

export default function VehiclesIndex({ vehicles, filters }: VehiclesIndexProps) {
    const [search, setSearch] = useState(filters.search || '');
    const [typeFilter, setTypeFilter] = useState(filters.type || 'all');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/admin/vehicles', { 
            search, 
            type: typeFilter === 'all' ? '' : typeFilter 
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const handleTypeFilter = (type: string) => {
        setTypeFilter(type);
        router.get('/admin/vehicles', { 
            search, 
            type: type === 'all' ? '' : type 
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const getVehicleTypeLabel = (type: string) => {
        switch (type) {
            case 'car': return 'Voiture';
            case 'suv': return 'SUV';
            case 'truck': return 'Camion';
            case 'motorcycle': return 'Moto';
            case 'van': return 'Fourgon';
            default: return type;
        }
    };

    const getVehicleTypeColor = (type: string) => {
        switch (type) {
            case 'car': return 'bg-blue-100 text-blue-800';
            case 'suv': return 'bg-green-100 text-green-800';
            case 'truck': return 'bg-orange-100 text-orange-800';
            case 'motorcycle': return 'bg-purple-100 text-purple-800';
            case 'van': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <AppLayout>
            <Head title="Gestion des Véhicules - AutoWash Admin" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Gestion des Véhicules</h1>
                        <p className="text-gray-600">Gérez tous les véhicules ({vehicles.meta.total} véhicules)</p>
                    </div>
                </div>

                {/* Filtres et recherche */}
                <Card>
                    <CardContent className="p-4">
                        <div className="flex flex-col lg:flex-row gap-4">
                            <form onSubmit={handleSearch} className="flex gap-4 flex-1">
                                <div className="flex-1">
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                        <Input
                                            type="text"
                                            placeholder="Rechercher par marque, modèle, plaque ou propriétaire..."
                                            value={search}
                                            onChange={(e) => setSearch(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                <Button type="submit">Rechercher</Button>
                            </form>
                            
                            <div className="flex items-center gap-2">
                                <Filter className="w-4 h-4 text-gray-500" />
                                <Select value={typeFilter} onValueChange={handleTypeFilter}>
                                    <SelectTrigger className="w-40">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Tous types</SelectItem>
                                        <SelectItem value="car">Voitures</SelectItem>
                                        <SelectItem value="suv">SUV</SelectItem>
                                        <SelectItem value="truck">Camions</SelectItem>
                                        <SelectItem value="motorcycle">Motos</SelectItem>
                                        <SelectItem value="van">Fourgons</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            {(filters.search || filters.type) && (
                                <Button 
                                    variant="outline"
                                    onClick={() => {
                                        setSearch('');
                                        setTypeFilter('all');
                                        router.get('/admin/vehicles');
                                    }}
                                >
                                    Effacer
                                </Button>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Liste des véhicules */}
                {vehicles.data.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {vehicles.data.map((vehicle) => (
                            <Card key={vehicle.id} className="hover:shadow-md transition-shadow">
                                <CardHeader className="pb-3">
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-3">
                                            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <Car className="w-5 h-5 text-blue-600" />
                                            </div>
                                            <div>
                                                <CardTitle className="text-base">
                                                    {vehicle.brand} {vehicle.model}
                                                </CardTitle>
                                                <p className="text-sm text-gray-600">{vehicle.year}</p>
                                            </div>
                                        </div>
                                        <Badge className={getVehicleTypeColor(vehicle.vehicle_type)}>
                                            {getVehicleTypeLabel(vehicle.vehicle_type)}
                                        </Badge>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Détails du véhicule */}
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span className="text-gray-600">Plaque:</span>
                                            <span className="font-mono bg-gray-100 px-2 py-1 rounded text-xs">
                                                {vehicle.license_plate}
                                            </span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span className="text-gray-600">Couleur:</span>
                                            <span className="font-medium">{vehicle.color}</span>
                                        </div>
                                    </div>

                                    {/* Propriétaire */}
                                    <div className="border-t pt-3">
                                        <div className="flex items-center gap-2 mb-2">
                                            <User className="w-4 h-4 text-gray-400" />
                                            <span className="text-sm font-medium">Propriétaire</span>
                                        </div>
                                        <p className="text-sm font-medium">{vehicle.user.name}</p>
                                        <p className="text-xs text-gray-600">{vehicle.user.email}</p>
                                    </div>

                                    {/* Statistiques */}
                                    <div className="border-t pt-3">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-1">
                                                <Calendar className="w-4 h-4 text-gray-400" />
                                                <span className="text-sm text-gray-600">Rendez-vous:</span>
                                            </div>
                                            <span className="text-lg font-bold text-green-600">
                                                {vehicle.appointments_count}
                                            </span>
                                        </div>
                                        <p className="text-xs text-gray-500 mt-1">
                                            Ajouté le {new Date(vehicle.created_at).toLocaleDateString('fr-FR')}
                                        </p>
                                    </div>

                                    {/* Actions */}
                                    <div className="flex gap-2 pt-3">
                                        <Button asChild size="sm" className="flex-1">
                                            <Link href={`/admin/vehicles/${vehicle.id}`}>
                                                <Eye className="w-4 h-4 mr-1" />
                                                Voir
                                            </Link>
                                        </Button>
                                        <Button asChild size="sm" variant="outline">
                                            <Link href={`/admin/users/${vehicle.user.id}`}>
                                                <User className="w-4 h-4 mr-1" />
                                                Client
                                            </Link>
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Car className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun véhicule trouvé</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                {filters.search || filters.type 
                                    ? 'Aucun véhicule ne correspond à vos critères.'
                                    : 'Aucun véhicule enregistré pour le moment.'
                                }
                            </p>
                        </CardContent>
                    </Card>
                )}

                {/* Pagination */}
                {vehicles.links && vehicles.links.length > 3 && (
                    <div className="flex justify-center">
                        <div className="flex gap-2">
                            {vehicles.links.map((link, index) => (
                                <Button
                                    key={index}
                                    variant={link.active ? "default" : "outline"}
                                    size="sm"
                                    disabled={!link.url}
                                    onClick={() => link.url && router.get(link.url)}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
