import { Head, <PERSON> } from '@inertiajs/react';
import { Calendar, Plus, Car, Clock } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface Appointment {
    id: number;
    scheduled_at: string;
    service_type: string;
    status: string;
    price: number;
    vehicle: {
        brand: string;
        model: string;
        license_plate: string;
    };
}

interface AppointmentsIndexProps {
    appointments: Appointment[];
}

export default function AppointmentsIndex({ appointments = [] }: AppointmentsIndexProps) {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800';
            case 'in_progress':
                return 'bg-yellow-100 text-yellow-800';
            case 'pending':
                return 'bg-orange-100 text-orange-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed':
                return 'Terminé';
            case 'confirmed':
                return 'Confirmé';
            case 'in_progress':
                return 'En cours';
            case 'pending':
                return 'En attente';
            case 'cancelled':
                return 'Annulé';
            default:
                return status;
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <Head title="Mes Rendez-vous - AutoWash" />
            
            <div className="max-w-6xl mx-auto space-y-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Mes Rendez-vous</h1>
                        <p className="text-gray-600">Gérez vos rendez-vous de lavage</p>
                    </div>
                    <Button asChild>
                        <Link href="/client/appointments/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Nouveau rendez-vous
                        </Link>
                    </Button>
                </div>

                {appointments.length > 0 ? (
                    <div className="space-y-4">
                        {appointments.map((appointment) => (
                            <Card key={appointment.id} className="hover:shadow-lg transition-shadow">
                                <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-4">
                                            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                                <Car className="w-6 h-6 text-blue-600" />
                                            </div>
                                            <div>
                                                <h3 className="font-semibold text-lg">
                                                    {appointment.vehicle.brand} {appointment.vehicle.model}
                                                </h3>
                                                <p className="text-sm text-gray-600">
                                                    {appointment.vehicle.license_plate}
                                                </p>
                                                <div className="flex items-center gap-4 mt-1">
                                                    <span className="text-sm text-gray-600">
                                                        Service: {appointment.service_type}
                                                    </span>
                                                    <span className="text-sm font-medium text-green-600">
                                                        {appointment.price}€
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div className="text-right">
                                            <div className="flex items-center gap-2 mb-2">
                                                <Clock className="w-4 h-4 text-gray-400" />
                                                <span className="text-sm text-gray-600">
                                                    {new Date(appointment.scheduled_at).toLocaleDateString('fr-FR')} à{' '}
                                                    {new Date(appointment.scheduled_at).toLocaleTimeString('fr-FR', { 
                                                        hour: '2-digit', 
                                                        minute: '2-digit' 
                                                    })}
                                                </span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                                                    {getStatusText(appointment.status)}
                                                </span>
                                                <Button asChild size="sm" variant="outline">
                                                    <Link href={`/client/appointments/${appointment.id}`}>
                                                        Voir
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun rendez-vous</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Vous n'avez pas encore de rendez-vous programmé.
                            </p>
                            <div className="mt-6">
                                <Button asChild>
                                    <Link href="/client/appointments/create">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Prendre rendez-vous
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </div>
    );
}
