import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Calendar, Plus, Car, Clock, Filter } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';

interface Appointment {
    id: number;
    scheduled_at: string;
    service_type: string;
    status: string;
    price: number;
    vehicle: {
        brand: string;
        model: string;
        license_plate: string;
    };
}

interface AppointmentsIndexProps {
    appointments: Appointment[];
}

export default function AppointmentsIndex({ appointments = [] }: AppointmentsIndexProps) {
    const [statusFilter, setStatusFilter] = useState<string>('all');
    const [serviceFilter, setServiceFilter] = useState<string>('all');

    // Filtrer les rendez-vous
    const filteredAppointments = appointments.filter(appointment => {
        const statusMatch = statusFilter === 'all' || appointment.status === statusFilter;
        const serviceMatch = serviceFilter === 'all' || appointment.service_type === serviceFilter;
        return statusMatch && serviceMatch;
    });

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800';
            case 'in_progress':
                return 'bg-yellow-100 text-yellow-800';
            case 'pending':
                return 'bg-orange-100 text-orange-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed':
                return 'Terminé';
            case 'confirmed':
                return 'Confirmé';
            case 'in_progress':
                return 'En cours';
            case 'pending':
                return 'En attente';
            case 'cancelled':
                return 'Annulé';
            default:
                return status;
        }
    };

    return (
        <AppLayout>
            <Head title="Mes Rendez-vous - AutoWash" />

            <div className="max-w-6xl mx-auto space-y-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Mes Rendez-vous</h1>
                        <p className="text-gray-600">Gérez vos rendez-vous de lavage ({filteredAppointments.length} rendez-vous)</p>
                    </div>
                    <Button asChild>
                        <Link href="/client/appointments/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Nouveau rendez-vous
                        </Link>
                    </Button>
                </div>

                {/* Filtres */}
                {appointments.length > 0 && (
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                                <div className="flex items-center gap-2">
                                    <Filter className="w-4 h-4 text-gray-500" />
                                    <span className="text-sm font-medium text-gray-700">Filtres:</span>
                                </div>
                                <div className="flex flex-col sm:flex-row gap-3">
                                    <div className="flex items-center gap-2">
                                        <label className="text-sm text-gray-600">Statut:</label>
                                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                                            <SelectTrigger className="w-32">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">Tous</SelectItem>
                                                <SelectItem value="pending">En attente</SelectItem>
                                                <SelectItem value="confirmed">Confirmé</SelectItem>
                                                <SelectItem value="in_progress">En cours</SelectItem>
                                                <SelectItem value="completed">Terminé</SelectItem>
                                                <SelectItem value="cancelled">Annulé</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <label className="text-sm text-gray-600">Service:</label>
                                        <Select value={serviceFilter} onValueChange={setServiceFilter}>
                                            <SelectTrigger className="w-32">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">Tous</SelectItem>
                                                <SelectItem value="basic">Basique</SelectItem>
                                                <SelectItem value="premium">Premium</SelectItem>
                                                <SelectItem value="deluxe">Deluxe</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {filteredAppointments.length > 0 ? (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        {filteredAppointments.map((appointment) => (
                            <Card key={appointment.id} className="hover:shadow-md transition-shadow">
                                <CardContent className="p-4">
                                    <div className="space-y-3">
                                        {/* Header avec véhicule et statut */}
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <Car className="w-5 h-5 text-blue-600" />
                                                </div>
                                                <div>
                                                    <h3 className="font-semibold text-base">
                                                        {appointment.vehicle.brand} {appointment.vehicle.model}
                                                    </h3>
                                                    <p className="text-xs text-gray-500">
                                                        {appointment.vehicle.license_plate}
                                                    </p>
                                                </div>
                                            </div>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                                                {getStatusText(appointment.status)}
                                            </span>
                                        </div>

                                        {/* Informations du service */}
                                        <div className="flex items-center justify-between text-sm">
                                            <span className="text-gray-600">
                                                Service: <span className="font-medium">{appointment.service_type}</span>
                                            </span>
                                            <span className="font-bold text-green-600">
                                                {appointment.price} DH
                                            </span>
                                        </div>

                                        {/* Date et actions */}
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-1 text-sm text-gray-600">
                                                <Clock className="w-4 h-4" />
                                                <span>
                                                    {new Date(appointment.scheduled_at).toLocaleDateString('fr-FR')} à{' '}
                                                    {new Date(appointment.scheduled_at).toLocaleTimeString('fr-FR', {
                                                        hour: '2-digit',
                                                        minute: '2-digit'
                                                    })}
                                                </span>
                                            </div>
                                            <Button asChild size="sm" variant="outline">
                                                <Link href={`/client/appointments/${appointment.id}`}>
                                                    Voir
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun rendez-vous</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Vous n'avez pas encore de rendez-vous programmé.
                            </p>
                            <div className="mt-6">
                                <Button asChild>
                                    <Link href="/client/appointments/create">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Prendre rendez-vous
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
