import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { type Calendar, type CalendarEvent, type Category } from '@/types';
import { router } from '@inertiajs/react';
import { ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

interface CalendarViewProps {
    calendars: Calendar[];
    categories: Category[];
    initialEvents?: CalendarEvent[];
}

export function CalendarView({ calendars, categories, initialEvents = [] }: CalendarViewProps) {
    const [currentDate, setCurrentDate] = useState(new Date());
    const [view, setView] = useState<'month' | 'week' | 'day'>('month');
    const [events, setEvents] = useState<CalendarEvent[]>(initialEvents);
    const [selectedCalendars, setSelectedCalendars] = useState<number[]>(calendars.map(c => c.id));
    const [loading, setLoading] = useState(false);

    const fetchEvents = useCallback(async () => {
        setLoading(true);
        try {
            const startDate = new Date(currentDate);
            const endDate = new Date(currentDate);
            
            if (view === 'month') {
                startDate.setDate(1);
                endDate.setMonth(endDate.getMonth() + 1, 0);
            } else if (view === 'week') {
                const dayOfWeek = startDate.getDay();
                startDate.setDate(startDate.getDate() - dayOfWeek);
                endDate.setDate(startDate.getDate() + 6);
            }

            const params = new URLSearchParams({
                start: startDate.toISOString().split('T')[0],
                end: endDate.toISOString().split('T')[0],
            });

            const response = await fetch(`/api/events?${params}`);
            const data = await response.json();
            setEvents(data);
        } catch (error) {
            console.error('Failed to fetch events:', error);
        } finally {
            setLoading(false);
        }
    }, [currentDate, view]);

    useEffect(() => {
        fetchEvents();
    }, [fetchEvents]);

    const navigateDate = (direction: 'prev' | 'next') => {
        const newDate = new Date(currentDate);
        if (view === 'month') {
            newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        } else if (view === 'week') {
            newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        } else {
            newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
        }
        setCurrentDate(newDate);
    };

    const formatDateHeader = () => {
        const options: Intl.DateTimeFormatOptions = {
            year: 'numeric',
            month: 'long',
        };
        
        if (view === 'day') {
            options.day = 'numeric';
        }
        
        return currentDate.toLocaleDateString('fr-FR', options);
    };

    const handleCreateEvent = () => {
        router.visit('/events/create');
    };

    const filteredEvents = events.filter(event => 
        selectedCalendars.includes(parseInt(event.extendedProps.calendar))
    );

    return (
        <div className="space-y-6">
            {/* Calendar Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <h1 className="text-2xl font-bold">{formatDateHeader()}</h1>
                    <div className="flex items-center space-x-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigateDate('prev')}
                        >
                            <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigateDate('next')}
                        >
                            <ChevronRight className="h-4 w-4" />
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentDate(new Date())}
                        >
                            Aujourd'hui
                        </Button>
                    </div>
                </div>
                
                <div className="flex items-center space-x-4">
                    <Select value={view} onValueChange={(value: 'month' | 'week' | 'day') => setView(value)}>
                        <SelectTrigger className="w-32">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="month">Mois</SelectItem>
                            <SelectItem value="week">Semaine</SelectItem>
                            <SelectItem value="day">Jour</SelectItem>
                        </SelectContent>
                    </Select>
                    
                    <Button onClick={handleCreateEvent}>
                        <Plus className="h-4 w-4 mr-2" />
                        Nouvel événement
                    </Button>
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {/* Sidebar */}
                <div className="lg:col-span-1 space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle>Calendriers</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            {calendars.map((calendar) => (
                                <label key={calendar.id} className="flex items-center space-x-2 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        checked={selectedCalendars.includes(calendar.id)}
                                        onChange={(e) => {
                                            if (e.target.checked) {
                                                setSelectedCalendars([...selectedCalendars, calendar.id]);
                                            } else {
                                                setSelectedCalendars(selectedCalendars.filter(id => id !== calendar.id));
                                            }
                                        }}
                                        className="rounded"
                                    />
                                    <div
                                        className="w-3 h-3 rounded-full"
                                        style={{ backgroundColor: calendar.color }}
                                    />
                                    <span className="text-sm">{calendar.name}</span>
                                </label>
                            ))}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Événements à venir</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                {filteredEvents
                                    .filter(event => new Date(event.start) >= new Date())
                                    .slice(0, 5)
                                    .map((event) => (
                                        <div key={event.id} className="p-2 rounded border-l-4" style={{ borderLeftColor: event.backgroundColor }}>
                                            <div className="font-medium text-sm">{event.title}</div>
                                            <div className="text-xs text-muted-foreground">
                                                {new Date(event.start).toLocaleDateString('fr-FR')}
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Calendar Grid */}
                <div className="lg:col-span-3">
                    <Card>
                        <CardContent className="p-0">
                            {loading ? (
                                <div className="flex items-center justify-center h-96">
                                    <div className="text-muted-foreground">Chargement...</div>
                                </div>
                            ) : (
                                <SimpleCalendarGrid
                                    currentDate={currentDate}
                                    view={view}
                                    events={filteredEvents}
                                />
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
}

// Simple calendar grid component
function SimpleCalendarGrid({ currentDate, view, events }: { 
    currentDate: Date; 
    view: 'month' | 'week' | 'day'; 
    events: CalendarEvent[] 
}) {
    const getDaysInMonth = (date: Date) => {
        const year = date.getFullYear();
        const month = date.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        const days = [];
        
        // Add empty cells for days before the first day of the month
        for (let i = 0; i < startingDayOfWeek; i++) {
            days.push(null);
        }
        
        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            days.push(new Date(year, month, day));
        }
        
        return days;
    };

    const getEventsForDate = (date: Date) => {
        return events.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate.toDateString() === date.toDateString();
        });
    };

    if (view === 'month') {
        const days = getDaysInMonth(currentDate);
        const weekDays = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];

        return (
            <div className="p-4">
                {/* Week headers */}
                <div className="grid grid-cols-7 gap-1 mb-2">
                    {weekDays.map(day => (
                        <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
                            {day}
                        </div>
                    ))}
                </div>
                
                {/* Calendar grid */}
                <div className="grid grid-cols-7 gap-1">
                    {days.map((day, index) => (
                        <div
                            key={index}
                            className={`min-h-24 p-1 border rounded ${
                                day ? 'bg-background hover:bg-muted/50' : 'bg-muted/20'
                            } ${
                                day && day.toDateString() === new Date().toDateString() 
                                    ? 'bg-primary/10 border-primary' 
                                    : 'border-border'
                            }`}
                        >
                            {day && (
                                <>
                                    <div className="text-sm font-medium mb-1">
                                        {day.getDate()}
                                    </div>
                                    <div className="space-y-1">
                                        {getEventsForDate(day).slice(0, 3).map(event => (
                                            <div
                                                key={event.id}
                                                className="text-xs p-1 rounded truncate"
                                                style={{ 
                                                    backgroundColor: event.backgroundColor + '20',
                                                    borderLeft: `3px solid ${event.backgroundColor}`
                                                }}
                                                title={event.title}
                                            >
                                                {event.title}
                                            </div>
                                        ))}
                                        {getEventsForDate(day).length > 3 && (
                                            <div className="text-xs text-muted-foreground">
                                                +{getEventsForDate(day).length - 3} autres
                                            </div>
                                        )}
                                    </div>
                                </>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    // For week and day views, show a simple list
    return (
        <div className="p-4">
            <div className="space-y-2">
                {events.map(event => (
                    <div
                        key={event.id}
                        className="p-3 rounded border-l-4"
                        style={{ borderLeftColor: event.backgroundColor }}
                    >
                        <div className="font-medium">{event.title}</div>
                        <div className="text-sm text-muted-foreground">
                            {new Date(event.start).toLocaleString('fr-FR')} - {new Date(event.end).toLocaleString('fr-FR')}
                        </div>
                        {event.extendedProps.location && (
                            <div className="text-sm text-muted-foreground">📍 {event.extendedProps.location}</div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
}
