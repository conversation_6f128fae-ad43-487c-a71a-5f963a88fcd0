# Guide de développement SmartCalendar

Ce document fournit des informations détaillées pour les développeurs travaillant sur SmartCalendar.

## Architecture

### Backend (Laravel)

#### Modèles
- **User** : Utilisateurs de l'application
- **Calendar** : Calendriers appartenant aux utilisateurs
- **Category** : Catégories pour organiser les événements
- **Event** : Événements avec support de récurrence

#### Services
- **RecurrenceService** : Gère la génération d'événements récurrents
- **NotificationService** (Frontend) : Gère les notifications utilisateur

#### Contrôleurs
- **EventController** : CRUD pour les événements + API
- **CalendarController** : CRUD pour les calendriers + API
- **CategoryController** : CRUD pour les catégories + API

#### Politiques d'autorisation
- **EventPolicy** : Contrôle l'accès aux événements
- **CalendarPolicy** : Contrôle l'accès aux calendriers
- **CategoryPolicy** : Contrôle l'accès aux catégories

### Frontend (React + TypeScript)

#### Structure des composants
```
components/
├── calendar/
│   ├── calendar-view.tsx      # Vue principale du calendrier
│   ├── event-form.tsx         # Formulaire d'événement
│   ├── event-search.tsx       # Recherche d'événements
│   └── calendar-stats.tsx     # Statistiques du calendrier
├── notifications/
│   └── notification-center.tsx # Centre de notifications
└── ui/                        # Composants UI réutilisables
```

#### Types TypeScript
Tous les types sont définis dans `resources/js/types/index.d.ts` :
- `Event` : Structure d'un événement
- `Calendar` : Structure d'un calendrier
- `Category` : Structure d'une catégorie
- `CalendarEvent` : Format d'événement pour l'affichage

## Base de données

### Migrations
Les migrations sont organisées par ordre chronologique :
1. `create_categories_table` : Table des catégories
2. `create_calendars_table` : Table des calendriers
3. `create_events_table` : Table des événements avec récurrence

### Relations
- User → hasMany → Calendars, Events, Categories
- Calendar → hasMany → Events
- Category → hasMany → Events
- Event → belongsTo → User, Calendar, Category

### Index
Les index suivants sont créés pour optimiser les performances :
- `events(start_date, end_date)` : Requêtes par plage de dates
- `events(user_id, start_date)` : Événements d'un utilisateur

## API Endpoints

### Événements
- `GET /api/events` : Liste des événements avec filtres
  - Paramètres : `start`, `end`, `calendar_id`, `category_id`
- `GET /events` : Page d'index des événements
- `POST /events` : Créer un événement
- `PUT /events/{id}` : Modifier un événement
- `DELETE /events/{id}` : Supprimer un événement

### Calendriers
- `GET /api/calendars` : Liste des calendriers
- `GET /calendars` : Page d'index des calendriers
- `POST /calendars` : Créer un calendrier
- `PUT /calendars/{id}` : Modifier un calendrier
- `DELETE /calendars/{id}` : Supprimer un calendrier

### Catégories
- `GET /api/categories` : Liste des catégories
- `GET /categories` : Page d'index des catégories
- `POST /categories` : Créer une catégorie
- `PUT /categories/{id}` : Modifier une catégorie
- `DELETE /categories/{id}` : Supprimer une catégorie

## Fonctionnalités avancées

### Récurrence d'événements
Le `RecurrenceService` gère la génération d'instances d'événements récurrents :
- Types supportés : daily, weekly, monthly, yearly
- Paramètres : interval, end_date, count, days (pour weekly)
- Génération à la demande pour optimiser les performances

### Notifications
Système de notifications côté client avec :
- Notifications de rappel (15 min avant)
- Notifications prioritaires (1h avant pour haute priorité)
- Stockage local avec persistance
- Support des notifications navigateur

### Recherche
Recherche avancée avec filtres :
- Recherche textuelle (titre, description, lieu)
- Filtres par calendrier et catégorie
- Résultats paginés et optimisés

## Tests

### Tests unitaires
- `RecurrenceServiceTest` : Tests du service de récurrence
- Tests des modèles et relations

### Tests d'intégration
- `EventControllerTest` : Tests CRUD des événements
- Tests d'autorisation et sécurité

### Exécution des tests
```bash
php artisan test
```

## Configuration

### Variables d'environnement
```env
# Limites de l'application
SMARTCALENDAR_MAX_EVENTS_PER_USER=10000
SMARTCALENDAR_MAX_CALENDARS_PER_USER=50
SMARTCALENDAR_MAX_CATEGORIES_PER_USER=100

# Cache
SMARTCALENDAR_EVENTS_CACHE_TTL=300
SMARTCALENDAR_CALENDARS_CACHE_TTL=3600

# Fonctionnalités
SMARTCALENDAR_RECURRING_EVENTS=true
SMARTCALENDAR_PUBLIC_CALENDARS=true
SMARTCALENDAR_EVENT_SEARCH=true
```

### Configuration personnalisée
Le fichier `config/smartcalendar.php` contient toutes les options configurables.

## Optimisations de performance

### Base de données
- Index sur les colonnes fréquemment requêtées
- Eager loading des relations
- Pagination des gros résultats

### Frontend
- Lazy loading des composants
- Mise en cache des requêtes API
- Optimisation des re-renders React

### Middleware
- `OptimizeCalendarQueries` : Optimise les requêtes calendrier
- Logging des requêtes lentes en développement

## Sécurité

### Autorisation
- Policies Laravel pour tous les modèles
- Vérification de propriété des ressources
- Protection CSRF sur tous les formulaires

### Validation
- Validation côté serveur et client
- Règles personnalisées pour les dates et couleurs
- Sanitisation des entrées utilisateur

## Déploiement

### Prérequis production
- PHP 8.2+
- MySQL/PostgreSQL (recommandé pour la production)
- Redis (pour le cache et les sessions)
- Node.js 18+ (pour le build)

### Commandes de déploiement
```bash
composer install --optimize-autoloader --no-dev
npm ci
npm run build
php artisan migrate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## Contribution

### Standards de code
- PSR-12 pour PHP
- ESLint + Prettier pour TypeScript/React
- Tests requis pour nouvelles fonctionnalités

### Workflow Git
1. Créer une branche feature
2. Développer avec tests
3. Pull request avec review
4. Merge après validation

### Debugging
- Laravel Telescope (développement)
- Query logging activé en local
- React DevTools pour le frontend

## Roadmap

### Fonctionnalités futures
- [ ] Export/Import iCal
- [ ] Partage de calendriers
- [ ] Intégration calendriers externes
- [ ] Application mobile
- [ ] Notifications push
- [ ] Synchronisation temps réel

### Améliorations techniques
- [ ] Cache Redis
- [ ] Queue pour notifications
- [ ] API GraphQL
- [ ] PWA support
- [ ] Internationalisation
