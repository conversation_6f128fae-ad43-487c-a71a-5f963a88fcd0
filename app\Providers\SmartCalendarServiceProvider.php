<?php

namespace App\Providers;

use App\Models\Calendar;
use App\Models\Category;
use App\Models\Event;
use App\Policies\CalendarPolicy;
use App\Policies\CategoryPolicy;
use App\Policies\EventPolicy;
use App\Services\RecurrenceService;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class SmartCalendarServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the RecurrenceService as a singleton
        $this->app->singleton(RecurrenceService::class, function ($app) {
            return new RecurrenceService();
        });

        // Merge configuration
        $this->mergeConfigFrom(
            __DIR__.'/../../config/smartcalendar.php', 'smartcalendar'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register policies
        Gate::policy(Event::class, EventPolicy::class);
        Gate::policy(Calendar::class, CalendarPolicy::class);
        Gate::policy(Category::class, CategoryPolicy::class);

        // Publish configuration
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/../../config/smartcalendar.php' => config_path('smartcalendar.php'),
            ], 'smartcalendar-config');
        }

        // Register custom validation rules
        $this->registerValidationRules();

        // Register model observers if needed
        $this->registerModelObservers();
    }

    /**
     * Register custom validation rules.
     */
    private function registerValidationRules(): void
    {
        // Custom validation rule for hex colors
        \Illuminate\Support\Facades\Validator::extend('hex_color', function ($attribute, $value, $parameters, $validator) {
            return preg_match('/^#[0-9A-Fa-f]{6}$/', $value);
        });

        // Custom validation rule for date ranges
        \Illuminate\Support\Facades\Validator::extend('date_range_limit', function ($attribute, $value, $parameters, $validator) {
            $maxDays = config('smartcalendar.events.max_date_range_days', 365);
            $startDate = $validator->getData()['start_date'] ?? null;
            
            if (!$startDate) {
                return true;
            }

            $start = \Carbon\Carbon::parse($startDate);
            $end = \Carbon\Carbon::parse($value);
            
            return $end->diffInDays($start) <= $maxDays;
        });

        // Custom validation rule for recurrence end date
        \Illuminate\Support\Facades\Validator::extend('recurrence_end_after_start', function ($attribute, $value, $parameters, $validator) {
            $startDate = $validator->getData()['start_date'] ?? null;
            
            if (!$startDate || !$value) {
                return true;
            }

            return \Carbon\Carbon::parse($value)->gt(\Carbon\Carbon::parse($startDate));
        });
    }

    /**
     * Register model observers.
     */
    private function registerModelObservers(): void
    {
        // You can register observers here if needed
        // Event::observe(EventObserver::class);
    }
}
