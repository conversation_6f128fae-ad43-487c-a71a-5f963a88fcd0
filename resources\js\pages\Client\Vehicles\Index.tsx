import { Head, Link, router } from '@inertiajs/react';
import { Car, Plus, Edit, Trash2, Eye, Calendar, AlertTriangle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';

interface Vehicle {
    id: number;
    brand: string;
    model: string;
    year: number;
    color: string;
    license_plate: string;
    vehicle_type: string;
    notes?: string;
    appointments_count?: number;
    last_appointment?: string;
    created_at: string;
}

interface VehiclesIndexProps {
    vehicles: Vehicle[];
}

export default function VehiclesIndex({ vehicles }: VehiclesIndexProps) {
    const handleDeleteRequest = (vehicle: Vehicle) => {
        const hasAppointments = vehicle.appointments_count && vehicle.appointments_count > 0;

        if (hasAppointments) {
            if (confirm(`Ce véhicule a ${vehicle.appointments_count} rendez-vous associé(s). Êtes-vous sûr de vouloir demander sa suppression ?`)) {
                router.post(`/client/vehicles/${vehicle.id}/request-deletion`, {}, {
                    onSuccess: () => {
                        // Redirection gérée par le contrôleur
                    }
                });
            }
        } else {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce véhicule ?')) {
                router.delete(`/client/vehicles/${vehicle.id}`, {
                    onSuccess: () => {
                        // Redirection gérée par le contrôleur
                    }
                });
            }
        }
    };

    const getVehicleTypeLabel = (type: string) => {
        switch (type) {
            case 'car': return 'Voiture';
            case 'suv': return 'SUV';
            case 'truck': return 'Camion';
            case 'motorcycle': return 'Moto';
            case 'van': return 'Fourgon';
            default: return type;
        }
    };

    return (
        <AppLayout>
            <Head title="Mes Véhicules - AutoWash" />

            <div className="space-y-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Mes Véhicules</h1>
                        <p className="text-gray-600">Gérez vos véhicules enregistrés ({vehicles.length} véhicule{vehicles.length > 1 ? 's' : ''})</p>
                    </div>
                    <Button asChild>
                        <Link href="/client/vehicles/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Ajouter un véhicule
                        </Link>
                    </Button>
                </div>

                {vehicles.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {vehicles.map((vehicle) => (
                            <Card key={vehicle.id} className="hover:shadow-lg transition-shadow">
                                <CardHeader className="pb-3">
                                    <div className="flex items-start justify-between">
                                        <CardTitle className="flex items-center gap-2">
                                            <Car className="h-5 w-5" />
                                            {vehicle.brand} {vehicle.model}
                                        </CardTitle>
                                        <Badge variant="secondary" className="text-xs">
                                            {getVehicleTypeLabel(vehicle.vehicle_type)}
                                        </Badge>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span className="text-gray-600">Année:</span>
                                            <span className="font-medium">{vehicle.year}</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span className="text-gray-600">Couleur:</span>
                                            <span className="font-medium">{vehicle.color}</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span className="text-gray-600">Plaque:</span>
                                            <span className="font-mono bg-gray-100 px-2 py-1 rounded text-xs">
                                                {vehicle.license_plate}
                                            </span>
                                        </div>
                                        {vehicle.appointments_count !== undefined && (
                                            <div className="flex justify-between text-sm">
                                                <span className="text-gray-600">Rendez-vous:</span>
                                                <span className="font-medium text-blue-600">
                                                    {vehicle.appointments_count}
                                                </span>
                                            </div>
                                        )}
                                        {vehicle.last_appointment && (
                                            <div className="flex justify-between text-sm">
                                                <span className="text-gray-600">Dernier lavage:</span>
                                                <span className="text-xs text-gray-500">
                                                    {new Date(vehicle.last_appointment).toLocaleDateString('fr-FR')}
                                                </span>
                                            </div>
                                        )}
                                    </div>

                                    {vehicle.notes && (
                                        <div className="border-t pt-3">
                                            <p className="text-xs text-gray-600">
                                                <span className="font-medium">Notes:</span> {vehicle.notes}
                                            </p>
                                        </div>
                                    )}

                                    <div className="flex flex-col gap-2">
                                        <div className="flex gap-2">
                                            <Button asChild size="sm" variant="outline" className="flex-1">
                                                <Link href={`/client/vehicles/${vehicle.id}`}>
                                                    <Eye className="h-4 w-4 mr-1" />
                                                    Voir
                                                </Link>
                                            </Button>
                                            <Button asChild size="sm" variant="outline">
                                                <Link href={`/client/vehicles/${vehicle.id}/edit`}>
                                                    <Edit className="h-4 w-4" />
                                                </Link>
                                            </Button>
                                            <Button asChild size="sm" variant="outline">
                                                <Link href={`/client/appointments/create?vehicle_id=${vehicle.id}`}>
                                                    <Calendar className="h-4 w-4" />
                                                </Link>
                                            </Button>
                                        </div>
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            className="text-red-600 hover:text-red-700 hover:border-red-300"
                                            onClick={() => handleDeleteRequest(vehicle)}
                                        >
                                            {vehicle.appointments_count && vehicle.appointments_count > 0 ? (
                                                <>
                                                    <AlertTriangle className="h-4 w-4 mr-1" />
                                                    Demander suppression
                                                </>
                                            ) : (
                                                <>
                                                    <Trash2 className="h-4 w-4 mr-1" />
                                                    Supprimer
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Car className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun véhicule</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Commencez par ajouter votre premier véhicule.
                            </p>
                            <div className="mt-6">
                                <Button asChild>
                                    <Link href={route('client.vehicles.create')}>
                                        <Plus className="mr-2 h-4 w-4" />
                                        Ajouter un véhicule
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
