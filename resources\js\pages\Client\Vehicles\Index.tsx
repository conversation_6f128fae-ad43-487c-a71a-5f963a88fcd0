import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Car, Plus, Edit, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';

interface Vehicle {
    id: number;
    brand: string;
    model: string;
    year: number;
    color: string;
    license_plate: string;
    vehicle_type: string;
    notes?: string;
}

interface VehiclesIndexProps {
    vehicles: Vehicle[];
}

export default function VehiclesIndex({ vehicles }: VehiclesIndexProps) {
    return (
        <AppLayout>
            <Head title="Mes Véhicules - AutoWash" />
            
            <div className="space-y-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Mes Véhicules</h1>
                        <p className="text-gray-600">Gérez vos véhicules enregistrés</p>
                    </div>
                    <Button asChild>
                        <Link href={route('client.vehicles.create')}>
                            <Plus className="mr-2 h-4 w-4" />
                            Ajouter un véhicule
                        </Link>
                    </Button>
                </div>

                {vehicles.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {vehicles.map((vehicle) => (
                            <Card key={vehicle.id} className="hover:shadow-lg transition-shadow">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Car className="h-5 w-5" />
                                        {vehicle.brand} {vehicle.model}
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <p className="text-sm text-gray-600">
                                            <span className="font-medium">Année:</span> {vehicle.year}
                                        </p>
                                        <p className="text-sm text-gray-600">
                                            <span className="font-medium">Couleur:</span> {vehicle.color}
                                        </p>
                                        <p className="text-sm text-gray-600">
                                            <span className="font-medium">Plaque:</span> {vehicle.license_plate}
                                        </p>
                                        <p className="text-sm text-gray-600">
                                            <span className="font-medium">Type:</span> {vehicle.vehicle_type}
                                        </p>
                                        {vehicle.notes && (
                                            <p className="text-sm text-gray-600">
                                                <span className="font-medium">Notes:</span> {vehicle.notes}
                                            </p>
                                        )}
                                    </div>
                                    
                                    <div className="flex gap-2">
                                        <Button asChild size="sm" variant="outline" className="flex-1">
                                            <Link href={route('client.vehicles.show', vehicle.id)}>
                                                Voir
                                            </Link>
                                        </Button>
                                        <Button asChild size="sm" variant="outline">
                                            <Link href={route('client.vehicles.edit', vehicle.id)}>
                                                <Edit className="h-4 w-4" />
                                            </Link>
                                        </Button>
                                        <Button asChild size="sm" variant="outline">
                                            <Link href={route('client.appointments.create', { vehicle_id: vehicle.id })}>
                                                RDV
                                            </Link>
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Car className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun véhicule</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Commencez par ajouter votre premier véhicule.
                            </p>
                            <div className="mt-6">
                                <Button asChild>
                                    <Link href={route('client.vehicles.create')}>
                                        <Plus className="mr-2 h-4 w-4" />
                                        Ajouter un véhicule
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
