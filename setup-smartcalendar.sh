#!/bin/bash

echo "Setting up SmartCalendar application..."
echo

echo "Installing PHP dependencies..."
composer install
if [ $? -ne 0 ]; then
    echo "Error installing PHP dependencies"
    exit 1
fi

echo
echo "Installing Node.js dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "Error installing Node.js dependencies"
    exit 1
fi

echo
echo "Setting up environment file..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "Environment file created"
else
    echo "Environment file already exists"
fi

echo
echo "Generating application key..."
php artisan key:generate
if [ $? -ne 0 ]; then
    echo "Error generating application key"
    exit 1
fi

echo
echo "Running database migrations..."
php artisan migrate:fresh
if [ $? -ne 0 ]; then
    echo "Error running migrations"
    exit 1
fi

echo
echo "Seeding database with sample data..."
php artisan db:seed
if [ $? -ne 0 ]; then
    echo "Error seeding database"
    exit 1
fi

echo
echo "Building frontend assets..."
npm run build
if [ $? -ne 0 ]; then
    echo "Error building frontend assets"
    exit 1
fi

echo
echo "Setting up file permissions..."
chmod -R 755 storage bootstrap/cache
if [ $? -ne 0 ]; then
    echo "Warning: Could not set file permissions"
fi

echo
echo "========================================"
echo "SmartCalendar setup completed successfully!"
echo "========================================"
echo
echo "You can now start the development server with:"
echo "  php artisan serve"
echo
echo "And in another terminal, start the frontend development server with:"
echo "  npm run dev"
echo
echo "Default login credentials:"
echo "  Email: <EMAIL>"
echo "  Password: password"
echo
