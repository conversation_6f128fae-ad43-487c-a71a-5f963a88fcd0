import AppLogoIcon from '@/components/app-logo-icon';
import { Link } from '@inertiajs/react';
import { Calendar } from 'lucide-react';
import { type PropsWithChildren } from 'react';

interface AuthLayoutProps {
    name?: string;
    title?: string;
    description?: string;
}

export default function AuthSimpleLayout({ children, title, description }: PropsWithChildren<AuthLayoutProps>) {
    return (
        <div className="flex min-h-svh" style={{background: 'linear-gradient(135deg, #FDF6F0 0%, #F0F8FF 100%)'}}>
            {/* Left side - Branding */}
            <div className="hidden lg:flex lg:w-1/2 flex-col justify-center items-center p-12" style={{background: 'linear-gradient(135deg, #5DADE2 0%, #FF6B35 100%)'}}>
                <div className="text-center text-white">
                    <div className="mb-8 flex justify-center">
                        <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-white/20 backdrop-blur-sm">
                            <Calendar className="size-8 text-white" />
                        </div>
                    </div>
                    <h1 className="text-4xl font-bold mb-4">AutoWash Pro</h1>
                    <p className="text-xl text-white/90 mb-8">
                        Service de lavage automobile professionnel
                    </p>
                    <div className="space-y-4 text-white/80">
                        <div className="flex items-center gap-3">
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                            <span>Réservation en ligne 24h/24</span>
                        </div>
                        <div className="flex items-center gap-3">
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                            <span>Équipe professionnelle</span>
                        </div>
                        <div className="flex items-center gap-3">
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                            <span>Produits écologiques</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Right side - Form */}
            <div className="flex-1 flex items-center justify-center p-6 md:p-12">
                <div className="w-full max-w-md">
                    <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                        <div className="flex flex-col items-center gap-6 mb-8">
                            <Link href={route('home')} className="flex flex-col items-center gap-3 font-medium lg:hidden">
                                <div className="flex h-12 w-12 items-center justify-center rounded-xl" style={{background: 'linear-gradient(135deg, #5DADE2 0%, #FF6B35 100%)'}}>
                                    <Calendar className="size-6 text-white" />
                                </div>
                                <span className="text-lg font-bold text-gray-900">AutoWash Pro</span>
                            </Link>

                            <div className="space-y-2 text-center">
                                <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
                                <p className="text-gray-600">{description}</p>
                            </div>
                        </div>
                        {children}
                    </div>
                </div>
            </div>
        </div>
    );
}
