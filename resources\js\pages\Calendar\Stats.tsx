import { CalendarStats } from '@/components/calendar/calendar-stats';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type CalendarEvent } from '@/types';
import { Head } from '@inertiajs/react';
import { useEffect, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Calendar',
        href: '/calendar',
    },
    {
        title: 'Statistiques',
        href: '/calendar/stats',
    },
];

export default function CalendarStatsPage() {
    const [events, setEvents] = useState<CalendarEvent[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchEvents = async () => {
            try {
                // Fetch events for the last 3 months and next 3 months for comprehensive stats
                const threeMonthsAgo = new Date();
                threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
                const threeMonthsFromNow = new Date();
                threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);

                const params = new URLSearchParams({
                    start: threeMonthsAgo.toISOString().split('T')[0],
                    end: threeMonthsFromNow.toISOString().split('T')[0],
                });

                const response = await fetch(`/api/events?${params}`);
                const data = await response.json();
                setEvents(data);
            } catch (error) {
                console.error('Failed to fetch events:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchEvents();
    }, []);

    if (loading) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Statistiques du calendrier" />
                <div className="flex items-center justify-center h-96">
                    <div className="text-muted-foreground">Chargement des statistiques...</div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Statistiques du calendrier" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">Statistiques du calendrier</h1>
                        <p className="text-muted-foreground">
                            Analysez vos habitudes de planification et votre productivité
                        </p>
                    </div>
                </div>

                <CalendarStats events={events} />
            </div>
        </AppLayout>
    );
}
