<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display a listing of clients.
     */
    public function index(Request $request)
    {
        $query = User::where('role', 'client')
            ->withCount(['vehicles', 'appointments'])
            ->with(['appointments' => function($q) {
                $q->where('status', 'completed')->latest()->limit(1);
            }]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        $users = $query->latest()->paginate(15)->withQueryString();

        return Inertia::render('Admin/Users/<USER>', [
            'users' => $users->toArray(), // Convert to array to ensure proper structure
            'filters' => $request->only(['search']),
        ]);
    }

    /**
     * Display the specified client.
     */
    public function show(User $user)
    {
        // Load relationships
        $user->load([
            'vehicles',
            'appointments.vehicle'
        ]);

        // Calculate stats
        $stats = [
            'total_vehicles' => $user->vehicles->count(),
            'total_appointments' => $user->appointments->count(),
            'completed_appointments' => $user->appointments->where('status', 'completed')->count(),
            'pending_appointments' => $user->appointments->where('status', 'pending')->count(),
            'total_spent' => $user->appointments->where('status', 'completed')->sum('price'),
            'last_appointment' => $user->appointments->where('status', 'completed')->sortByDesc('scheduled_at')->first()?->scheduled_at,
        ];

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user,
            'stats' => $stats,
        ]);
    }
}
