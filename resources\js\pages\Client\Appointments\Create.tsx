import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Calendar, Car } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

interface Vehicle {
    id: number;
    brand: string;
    model: string;
    license_plate: string;
}

interface CreateAppointmentProps {
    vehicles: Vehicle[];
}

export default function CreateAppointment({ vehicles = [] }: CreateAppointmentProps) {
    const { data, setData, post, processing, errors } = useForm({
        vehicle_id: '',
        service_type: 'basic',
        scheduled_at: '',
        notes: '',
    });

    const submit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/client/appointments');
    };

    const services = [
        { value: 'basic', label: 'Lavage Basique', price: 15, description: 'Lavage extérieur + aspirateur' },
        { value: 'premium', label: 'Lavage Premium', price: 25, description: 'Lavage complet + cire + pneus' },
        { value: 'deluxe', label: 'Lavage Deluxe', price: 35, description: 'Lavage premium + intérieur + détailing' },
    ];

    // Date minimum : demain
    const minDate = new Date();
    minDate.setDate(minDate.getDate() + 1);
    const minDateString = minDate.toISOString().slice(0, 16);

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <Head title="Prendre rendez-vous - AutoWash" />
            
            <div className="max-w-2xl mx-auto space-y-6">
                <div className="flex items-center gap-4">
                    <Button asChild variant="outline" size="sm">
                        <Link href="/client/appointments">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Retour
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Prendre rendez-vous</h1>
                        <p className="text-gray-600">Planifiez votre lavage automobile</p>
                    </div>
                </div>

                {vehicles.length === 0 ? (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Car className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun véhicule</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Vous devez d'abord ajouter un véhicule pour prendre rendez-vous.
                            </p>
                            <div className="mt-6">
                                <Button asChild>
                                    <Link href="/client/vehicles/create">
                                        Ajouter un véhicule
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                ) : (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Détails du rendez-vous
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={submit} className="space-y-6">
                                <div className="space-y-2">
                                    <Label htmlFor="vehicle_id">Véhicule</Label>
                                    <Select value={data.vehicle_id} onValueChange={(value) => setData('vehicle_id', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Sélectionnez un véhicule" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {vehicles.map((vehicle) => (
                                                <SelectItem key={vehicle.id} value={vehicle.id.toString()}>
                                                    {vehicle.brand} {vehicle.model} ({vehicle.license_plate})
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.vehicle_id && <p className="text-sm text-red-600">{errors.vehicle_id}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="service_type">Type de service</Label>
                                    <div className="grid gap-3">
                                        {services.map((service) => (
                                            <div
                                                key={service.value}
                                                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                                                    data.service_type === service.value
                                                        ? 'border-blue-500 bg-blue-50'
                                                        : 'border-gray-200 hover:border-gray-300'
                                                }`}
                                                onClick={() => setData('service_type', service.value)}
                                            >
                                                <div className="flex justify-between items-start">
                                                    <div>
                                                        <h4 className="font-medium">{service.label}</h4>
                                                        <p className="text-sm text-gray-600">{service.description}</p>
                                                    </div>
                                                    <span className="text-lg font-bold text-green-600">
                                                        {service.price}€
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                    {errors.service_type && <p className="text-sm text-red-600">{errors.service_type}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="scheduled_at">Date et heure</Label>
                                    <Input
                                        id="scheduled_at"
                                        type="datetime-local"
                                        value={data.scheduled_at}
                                        onChange={(e) => setData('scheduled_at', e.target.value)}
                                        min={minDateString}
                                        required
                                    />
                                    <p className="text-xs text-gray-500">
                                        Horaires d'ouverture : 8h00 - 18h00, du lundi au samedi
                                    </p>
                                    {errors.scheduled_at && <p className="text-sm text-red-600">{errors.scheduled_at}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="notes">Notes (optionnel)</Label>
                                    <Textarea
                                        id="notes"
                                        value={data.notes}
                                        onChange={(e) => setData('notes', e.target.value)}
                                        placeholder="Instructions spéciales, demandes particulières..."
                                        rows={3}
                                    />
                                    {errors.notes && <p className="text-sm text-red-600">{errors.notes}</p>}
                                </div>

                                <div className="flex gap-4">
                                    <Button type="submit" disabled={processing} className="flex-1">
                                        {processing ? 'Création...' : 'Prendre rendez-vous'}
                                    </Button>
                                    <Button asChild variant="outline" type="button">
                                        <Link href="/client/appointments">Annuler</Link>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                )}
            </div>
        </div>
    );
}
