<?php

namespace App\Services;

use App\Models\Event;
use Carbon\Carbon;

class RecurrenceService
{
    /**
     * Generate recurring event instances for a given date range.
     */
    public function generateRecurringEvents(Event $event, Carbon $startDate, Carbon $endDate): array
    {
        if ($event->recurrence_type === 'none') {
            return [];
        }

        $instances = [];
        $currentDate = Carbon::parse($event->start_date);
        $eventDuration = $currentDate->diffInMinutes(Carbon::parse($event->end_date));
        
        // Ensure we don't go beyond the recurrence end date
        $maxEndDate = $event->recurrence_end_date 
            ? Carbon::parse($event->recurrence_end_date) 
            : $endDate;
        
        $count = 0;
        $maxCount = $event->recurrence_count ?: 1000; // Default max to prevent infinite loops

        while ($currentDate->lte($maxEndDate) && $currentDate->lte($endDate) && $count < $maxCount) {
            // Only include instances that fall within our requested range
            if ($currentDate->gte($startDate)) {
                $instanceEnd = $currentDate->copy()->addMinutes($eventDuration);
                
                $instances[] = [
                    'id' => $event->id . '_' . $currentDate->format('Y-m-d-H-i'),
                    'title' => $event->title,
                    'start' => $currentDate->toISOString(),
                    'end' => $instanceEnd->toISOString(),
                    'allDay' => $event->all_day,
                    'backgroundColor' => $event->calendar->color,
                    'borderColor' => $event->calendar->color,
                    'extendedProps' => [
                        'description' => $event->description,
                        'location' => $event->location,
                        'status' => $event->status,
                        'priority' => $event->priority,
                        'calendar' => $event->calendar->name,
                        'category' => $event->category?->name,
                        'isRecurring' => true,
                        'originalEventId' => $event->id,
                    ],
                ];
            }

            $currentDate = $this->getNextOccurrence($currentDate, $event);
            $count++;
        }

        return $instances;
    }

    /**
     * Get the next occurrence date based on recurrence rules.
     */
    private function getNextOccurrence(Carbon $currentDate, Event $event): Carbon
    {
        $nextDate = $currentDate->copy();

        switch ($event->recurrence_type) {
            case 'daily':
                $nextDate->addDays($event->recurrence_interval);
                break;

            case 'weekly':
                if ($event->recurrence_days && is_array($event->recurrence_days)) {
                    // Find the next day of the week in the recurrence_days array
                    $nextDate = $this->getNextWeeklyOccurrence($currentDate, $event->recurrence_days, $event->recurrence_interval);
                } else {
                    $nextDate->addWeeks($event->recurrence_interval);
                }
                break;

            case 'monthly':
                $nextDate->addMonths($event->recurrence_interval);
                break;

            case 'yearly':
                $nextDate->addYears($event->recurrence_interval);
                break;

            default:
                // If unknown recurrence type, just add one day to prevent infinite loop
                $nextDate->addDay();
                break;
        }

        return $nextDate;
    }

    /**
     * Get the next weekly occurrence based on specific days of the week.
     */
    private function getNextWeeklyOccurrence(Carbon $currentDate, array $daysOfWeek, int $interval): Carbon
    {
        $nextDate = $currentDate->copy();
        $currentDayOfWeek = $currentDate->dayOfWeek; // 0 = Sunday, 6 = Saturday
        
        // Sort days of week
        sort($daysOfWeek);
        
        // Find the next day in the current week
        $nextDayInWeek = null;
        foreach ($daysOfWeek as $day) {
            if ($day > $currentDayOfWeek) {
                $nextDayInWeek = $day;
                break;
            }
        }
        
        if ($nextDayInWeek !== null) {
            // Next occurrence is in the same week
            $daysToAdd = $nextDayInWeek - $currentDayOfWeek;
            $nextDate->addDays($daysToAdd);
        } else {
            // Next occurrence is in the next interval week
            $weeksToAdd = $interval;
            $nextDate->addWeeks($weeksToAdd);
            
            // Set to the first day of the week in the recurrence pattern
            $firstDay = min($daysOfWeek);
            $daysToAdd = $firstDay - $nextDate->dayOfWeek;
            if ($daysToAdd < 0) {
                $daysToAdd += 7;
            }
            $nextDate->addDays($daysToAdd);
        }
        
        return $nextDate;
    }

    /**
     * Check if an event should have recurring instances generated.
     */
    public function shouldGenerateRecurringInstances(Event $event): bool
    {
        return $event->recurrence_type !== 'none' && 
               $event->recurrence_interval > 0;
    }

    /**
     * Get all event instances (original + recurring) for a date range.
     */
    public function getEventInstances(Event $event, Carbon $startDate, Carbon $endDate): array
    {
        $instances = [];
        
        // Add the original event if it falls within the date range
        $eventStart = Carbon::parse($event->start_date);
        $eventEnd = Carbon::parse($event->end_date);
        
        if ($eventStart->between($startDate, $endDate) || 
            $eventEnd->between($startDate, $endDate) ||
            ($eventStart->lte($startDate) && $eventEnd->gte($endDate))) {
            
            $instances[] = [
                'id' => $event->id,
                'title' => $event->title,
                'start' => $eventStart->toISOString(),
                'end' => $eventEnd->toISOString(),
                'allDay' => $event->all_day,
                'backgroundColor' => $event->calendar->color,
                'borderColor' => $event->calendar->color,
                'extendedProps' => [
                    'description' => $event->description,
                    'location' => $event->location,
                    'status' => $event->status,
                    'priority' => $event->priority,
                    'calendar' => $event->calendar->name,
                    'category' => $event->category?->name,
                    'isRecurring' => false,
                    'originalEventId' => $event->id,
                ],
            ];
        }
        
        // Add recurring instances if applicable
        if ($this->shouldGenerateRecurringInstances($event)) {
            $recurringInstances = $this->generateRecurringEvents($event, $startDate, $endDate);
            $instances = array_merge($instances, $recurringInstances);
        }
        
        return $instances;
    }
}
