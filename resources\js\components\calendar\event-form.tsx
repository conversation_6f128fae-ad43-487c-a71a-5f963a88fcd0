import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { type Calendar, type Category, type Event } from '@/types';
import { useForm } from '@inertiajs/react';
import { CalendarIcon, Clock, MapPin, Tag } from 'lucide-react';

interface EventFormProps {
    event?: Event;
    calendars: Calendar[];
    categories: Category[];
    onCancel?: () => void;
}

export function EventForm({ event, calendars, categories, onCancel }: EventFormProps) {
    const { data, setData, post, put, processing, errors, reset } = useForm({
        title: event?.title || '',
        description: event?.description || '',
        start_date: event?.start_date ? new Date(event.start_date).toISOString().slice(0, 16) : '',
        end_date: event?.end_date ? new Date(event.end_date).toISOString().slice(0, 16) : '',
        all_day: event?.all_day || false,
        location: event?.location || '',
        status: event?.status || 'confirmed',
        priority: event?.priority || 'medium',
        calendar_id: event?.calendar_id || (calendars.find(c => c.is_default)?.id || calendars[0]?.id || ''),
        category_id: event?.category_id || '',
        recurrence_type: event?.recurrence_type || 'none',
        recurrence_interval: event?.recurrence_interval || 1,
        recurrence_days: event?.recurrence_days || [],
        recurrence_end_date: event?.recurrence_end_date || '',
        recurrence_count: event?.recurrence_count || '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (event) {
            put(`/events/${event.id}`, {
                onSuccess: () => {
                    // Handle success
                },
            });
        } else {
            post('/events', {
                onSuccess: () => {
                    reset();
                },
            });
        }
    };

    const handleAllDayChange = (checked: boolean) => {
        setData('all_day', checked);
        if (checked) {
            // Set times to full day
            const startDate = new Date(data.start_date);
            const endDate = new Date(data.end_date);
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
            setData({
                ...data,
                all_day: true,
                start_date: startDate.toISOString().slice(0, 16),
                end_date: endDate.toISOString().slice(0, 16),
            });
        }
    };

    return (
        <Card className="w-full max-w-2xl mx-auto">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <CalendarIcon className="h-5 w-5" />
                    {event ? 'Modifier l\'événement' : 'Nouvel événement'}
                </CardTitle>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Basic Information */}
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="title">Titre *</Label>
                            <Input
                                id="title"
                                value={data.title}
                                onChange={(e) => setData('title', e.target.value)}
                                placeholder="Titre de l'événement"
                                className={errors.title ? 'border-red-500' : ''}
                            />
                            {errors.title && <p className="text-sm text-red-500 mt-1">{errors.title}</p>}
                        </div>

                        <div>
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                id="description"
                                value={data.description}
                                onChange={(e) => setData('description', e.target.value)}
                                placeholder="Description de l'événement"
                                rows={3}
                            />
                        </div>
                    </div>

                    {/* Date and Time */}
                    <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="all_day"
                                checked={data.all_day}
                                onCheckedChange={handleAllDayChange}
                            />
                            <Label htmlFor="all_day">Toute la journée</Label>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="start_date" className="flex items-center gap-2">
                                    <Clock className="h-4 w-4" />
                                    Début *
                                </Label>
                                <Input
                                    id="start_date"
                                    type={data.all_day ? 'date' : 'datetime-local'}
                                    value={data.all_day ? data.start_date.split('T')[0] : data.start_date}
                                    onChange={(e) => setData('start_date', e.target.value)}
                                    className={errors.start_date ? 'border-red-500' : ''}
                                />
                                {errors.start_date && <p className="text-sm text-red-500 mt-1">{errors.start_date}</p>}
                            </div>

                            <div>
                                <Label htmlFor="end_date">Fin *</Label>
                                <Input
                                    id="end_date"
                                    type={data.all_day ? 'date' : 'datetime-local'}
                                    value={data.all_day ? data.end_date.split('T')[0] : data.end_date}
                                    onChange={(e) => setData('end_date', e.target.value)}
                                    className={errors.end_date ? 'border-red-500' : ''}
                                />
                                {errors.end_date && <p className="text-sm text-red-500 mt-1">{errors.end_date}</p>}
                            </div>
                        </div>
                    </div>

                    {/* Location */}
                    <div>
                        <Label htmlFor="location" className="flex items-center gap-2">
                            <MapPin className="h-4 w-4" />
                            Lieu
                        </Label>
                        <Input
                            id="location"
                            value={data.location}
                            onChange={(e) => setData('location', e.target.value)}
                            placeholder="Lieu de l'événement"
                        />
                    </div>

                    {/* Calendar and Category */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="calendar_id">Calendrier *</Label>
                            <Select value={data.calendar_id.toString()} onValueChange={(value) => setData('calendar_id', parseInt(value))}>
                                <SelectTrigger className={errors.calendar_id ? 'border-red-500' : ''}>
                                    <SelectValue placeholder="Sélectionner un calendrier" />
                                </SelectTrigger>
                                <SelectContent>
                                    {calendars.map((calendar) => (
                                        <SelectItem key={calendar.id} value={calendar.id.toString()}>
                                            <div className="flex items-center gap-2">
                                                <div
                                                    className="w-3 h-3 rounded-full"
                                                    style={{ backgroundColor: calendar.color }}
                                                />
                                                {calendar.name}
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.calendar_id && <p className="text-sm text-red-500 mt-1">{errors.calendar_id}</p>}
                        </div>

                        <div>
                            <Label htmlFor="category_id" className="flex items-center gap-2">
                                <Tag className="h-4 w-4" />
                                Catégorie
                            </Label>
                            <Select value={data.category_id?.toString() || ''} onValueChange={(value) => setData('category_id', value ? parseInt(value) : null)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Sélectionner une catégorie" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">Aucune catégorie</SelectItem>
                                    {categories.map((category) => (
                                        <SelectItem key={category.id} value={category.id.toString()}>
                                            <div className="flex items-center gap-2">
                                                <div
                                                    className="w-3 h-3 rounded-full"
                                                    style={{ backgroundColor: category.color }}
                                                />
                                                {category.name}
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Status and Priority */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="status">Statut</Label>
                            <Select value={data.status} onValueChange={(value) => setData('status', value as any)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="confirmed">Confirmé</SelectItem>
                                    <SelectItem value="tentative">Provisoire</SelectItem>
                                    <SelectItem value="cancelled">Annulé</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label htmlFor="priority">Priorité</Label>
                            <Select value={data.priority} onValueChange={(value) => setData('priority', value as any)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="low">Faible</SelectItem>
                                    <SelectItem value="medium">Moyenne</SelectItem>
                                    <SelectItem value="high">Élevée</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Recurrence */}
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="recurrence_type">Récurrence</Label>
                            <Select value={data.recurrence_type} onValueChange={(value) => setData('recurrence_type', value as any)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="none">Aucune</SelectItem>
                                    <SelectItem value="daily">Quotidienne</SelectItem>
                                    <SelectItem value="weekly">Hebdomadaire</SelectItem>
                                    <SelectItem value="monthly">Mensuelle</SelectItem>
                                    <SelectItem value="yearly">Annuelle</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        {data.recurrence_type !== 'none' && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="recurrence_interval">Intervalle</Label>
                                    <Input
                                        id="recurrence_interval"
                                        type="number"
                                        min="1"
                                        value={data.recurrence_interval}
                                        onChange={(e) => setData('recurrence_interval', parseInt(e.target.value))}
                                    />
                                </div>

                                <div>
                                    <Label htmlFor="recurrence_end_date">Fin de récurrence</Label>
                                    <Input
                                        id="recurrence_end_date"
                                        type="date"
                                        value={data.recurrence_end_date}
                                        onChange={(e) => setData('recurrence_end_date', e.target.value)}
                                    />
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Form Actions */}
                    <div className="flex justify-end space-x-4 pt-6 border-t">
                        {onCancel && (
                            <Button type="button" variant="outline" onClick={onCancel}>
                                Annuler
                            </Button>
                        )}
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Enregistrement...' : (event ? 'Modifier' : 'Créer')}
                        </Button>
                    </div>
                </form>
            </CardContent>
        </Card>
    );
}
