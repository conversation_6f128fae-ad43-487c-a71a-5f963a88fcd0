import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    [key: string]: unknown; // This allows for additional properties...
}

export interface Category {
    id: number;
    name: string;
    color: string;
    description?: string;
    user_id: number;
    created_at: string;
    updated_at: string;
}

export interface Calendar {
    id: number;
    name: string;
    description?: string;
    color: string;
    is_default: boolean;
    is_public: boolean;
    user_id: number;
    events_count?: number;
    created_at: string;
    updated_at: string;
}

export interface Event {
    id: number;
    title: string;
    description?: string;
    start_date: string;
    end_date: string;
    all_day: boolean;
    location?: string;
    status: 'confirmed' | 'tentative' | 'cancelled';
    priority: 'low' | 'medium' | 'high';
    recurrence_type: 'none' | 'daily' | 'weekly' | 'monthly' | 'yearly';
    recurrence_interval: number;
    recurrence_days?: number[];
    recurrence_end_date?: string;
    recurrence_count?: number;
    user_id: number;
    calendar_id: number;
    category_id?: number;
    calendar?: Calendar;
    category?: Category;
    metadata?: Record<string, any>;
    created_at: string;
    updated_at: string;
}

export interface CalendarEvent {
    id: number;
    title: string;
    start: string;
    end: string;
    allDay: boolean;
    backgroundColor: string;
    borderColor: string;
    extendedProps: {
        description?: string;
        location?: string;
        status: string;
        priority: string;
        calendar: string;
        category?: string;
    };
}
