import { Head } from '@inertiajs/react';
import { BarChart3, Users, Car, Calendar, TrendingUp, DollarSign } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';

interface GeneralStats {
    total_clients: number;
    total_vehicles: number;
    total_appointments: number;
    total_revenue: number;
}

interface MonthlyRevenue {
    period: string;
    revenue: number;
    appointments: number;
}

interface PopularService {
    service: string;
    count: number;
}

interface TopClient {
    id: number;
    name: string;
    email: string;
    appointments_count: number;
    total_spent: number;
}

interface AppointmentsByStatus {
    status: string;
    count: number;
}

interface StatsIndexProps {
    generalStats: GeneralStats;
    monthlyRevenue: MonthlyRevenue[];
    popularServices: PopularService[];
    topClients: TopClient[];
    appointmentsByStatus: AppointmentsByStatus[];
    period: string;
}

export default function StatsIndex({ 
    generalStats, 
    monthlyRevenue, 
    popularServices, 
    topClients, 
    appointmentsByStatus 
}: StatsIndexProps) {
    
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('fr-FR').format(amount) + ' DH';
    };

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'en attente': return 'bg-orange-100 text-orange-800';
            case 'confirmé': return 'bg-blue-100 text-blue-800';
            case 'en cours': return 'bg-yellow-100 text-yellow-800';
            case 'terminé': return 'bg-green-100 text-green-800';
            case 'annulé': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <AppLayout>
            <Head title="Statistiques - AutoWash Admin" />
            
            <div className="space-y-6">
                {/* Header */}
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Statistiques</h1>
                    <p className="text-gray-600">Vue d'ensemble des performances d'AutoWash</p>
                </div>

                {/* Statistiques générales */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{generalStats.total_clients}</div>
                            <p className="text-xs text-muted-foreground">
                                Clients enregistrés
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Véhicules</CardTitle>
                            <Car className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{generalStats.total_vehicles}</div>
                            <p className="text-xs text-muted-foreground">
                                Véhicules enregistrés
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Rendez-vous</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{generalStats.total_appointments}</div>
                            <p className="text-xs text-muted-foreground">
                                Rendez-vous programmés
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Chiffre d'Affaires</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(generalStats.total_revenue)}</div>
                            <p className="text-xs text-muted-foreground">
                                Revenus totaux
                            </p>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Revenus mensuels */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="h-5 w-5" />
                                Revenus des 12 derniers mois
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {monthlyRevenue.slice(0, 6).map((month, index) => (
                                    <div key={month.period} className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium">
                                                {new Date(month.period + '-01').toLocaleDateString('fr-FR', { 
                                                    month: 'long', 
                                                    year: 'numeric' 
                                                })}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {month.appointments} rendez-vous
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-bold text-green-600">
                                                {formatCurrency(month.revenue)}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Services populaires */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <BarChart3 className="h-5 w-5" />
                                Services les plus populaires
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {popularServices.map((service, index) => {
                                    const maxCount = Math.max(...popularServices.map(s => s.count));
                                    const percentage = (service.count / maxCount) * 100;
                                    
                                    return (
                                        <div key={service.service}>
                                            <div className="flex items-center justify-between mb-1">
                                                <span className="text-sm font-medium">{service.service}</span>
                                                <span className="text-sm text-gray-600">{service.count}</span>
                                            </div>
                                            <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div 
                                                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                                    style={{ width: `${percentage}%` }}
                                                ></div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Top clients */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Users className="h-5 w-5" />
                                Clients les plus actifs
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {topClients.slice(0, 5).map((client, index) => (
                                    <div key={client.id} className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                <span className="text-sm font-medium text-blue-600">
                                                    {index + 1}
                                                </span>
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium">{client.name}</p>
                                                <p className="text-xs text-gray-500">{client.email}</p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-bold">{client.appointments_count} RDV</p>
                                            <p className="text-xs text-green-600">
                                                {formatCurrency(client.total_spent)}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Rendez-vous par statut */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Répartition par statut
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {appointmentsByStatus.map((item) => {
                                    const total = appointmentsByStatus.reduce((sum, s) => sum + s.count, 0);
                                    const percentage = total > 0 ? (item.count / total) * 100 : 0;
                                    
                                    return (
                                        <div key={item.status} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                                                    {item.status}
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-bold">{item.count}</p>
                                                <p className="text-xs text-gray-500">
                                                    {percentage.toFixed(1)}%
                                                </p>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
