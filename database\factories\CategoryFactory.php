<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->randomElement([
                'Travail', 'Personnel', 'Famille', 'Sport', 'Santé',
                'Éducation', 'Voyage', 'Loisirs', 'Réunions', 'Projets'
            ]),
            'color' => fake()->randomElement([
                '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
                '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
            ]),
            'description' => fake()->sentence(),
            'user_id' => User::factory(),
        ];
    }
}
