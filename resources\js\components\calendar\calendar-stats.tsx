import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { type CalendarEvent } from '@/types';
import { BarChart3, Calendar, Clock, TrendingUp } from 'lucide-react';
import { useEffect, useState } from 'react';

interface CalendarStatsProps {
    events?: CalendarEvent[];
}

interface Stats {
    totalEvents: number;
    thisWeekEvents: number;
    thisMonthEvents: number;
    upcomingEvents: number;
    eventsByPriority: {
        high: number;
        medium: number;
        low: number;
    };
    eventsByStatus: {
        confirmed: number;
        tentative: number;
        cancelled: number;
    };
    averageEventsPerWeek: number;
    busyDays: number;
}

export function CalendarStats({ events = [] }: CalendarStatsProps) {
    const [stats, setStats] = useState<Stats>({
        totalEvents: 0,
        thisWeekEvents: 0,
        thisMonthEvents: 0,
        upcomingEvents: 0,
        eventsByPriority: { high: 0, medium: 0, low: 0 },
        eventsByStatus: { confirmed: 0, tentative: 0, cancelled: 0 },
        averageEventsPerWeek: 0,
        busyDays: 0,
    });

    useEffect(() => {
        if (events.length === 0) return;

        const now = new Date();
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);

        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        const thisWeekEvents = events.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate >= startOfWeek && eventDate <= endOfWeek;
        });

        const thisMonthEvents = events.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate >= startOfMonth && eventDate <= endOfMonth;
        });

        const upcomingEvents = events.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate > now;
        });

        const eventsByPriority = events.reduce(
            (acc, event) => {
                const priority = event.extendedProps.priority || 'medium';
                acc[priority as keyof typeof acc]++;
                return acc;
            },
            { high: 0, medium: 0, low: 0 }
        );

        const eventsByStatus = events.reduce(
            (acc, event) => {
                const status = event.extendedProps.status || 'confirmed';
                acc[status as keyof typeof acc]++;
                return acc;
            },
            { confirmed: 0, tentative: 0, cancelled: 0 }
        );

        // Calculate busy days (days with more than 2 events)
        const eventsByDate = events.reduce((acc, event) => {
            const dateKey = new Date(event.start).toDateString();
            acc[dateKey] = (acc[dateKey] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const busyDays = Object.values(eventsByDate).filter(count => count > 2).length;

        // Calculate average events per week (last 4 weeks)
        const fourWeeksAgo = new Date(now);
        fourWeeksAgo.setDate(now.getDate() - 28);
        const recentEvents = events.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate >= fourWeeksAgo && eventDate <= now;
        });
        const averageEventsPerWeek = Math.round(recentEvents.length / 4);

        setStats({
            totalEvents: events.length,
            thisWeekEvents: thisWeekEvents.length,
            thisMonthEvents: thisMonthEvents.length,
            upcomingEvents: upcomingEvents.length,
            eventsByPriority,
            eventsByStatus,
            averageEventsPerWeek,
            busyDays,
        });
    }, [events]);

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'high': return 'text-red-600';
            case 'medium': return 'text-yellow-600';
            case 'low': return 'text-green-600';
            default: return 'text-gray-600';
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'confirmed': return 'text-green-600';
            case 'tentative': return 'text-yellow-600';
            case 'cancelled': return 'text-red-600';
            default: return 'text-gray-600';
        }
    };

    return (
        <div className="space-y-6">
            {/* Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total événements</CardTitle>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.totalEvents}</div>
                        <p className="text-xs text-muted-foreground">
                            Tous les événements
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Cette semaine</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.thisWeekEvents}</div>
                        <p className="text-xs text-muted-foreground">
                            Événements cette semaine
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">À venir</CardTitle>
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.upcomingEvents}</div>
                        <p className="text-xs text-muted-foreground">
                            Événements futurs
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Moyenne/semaine</CardTitle>
                        <BarChart3 className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.averageEventsPerWeek}</div>
                        <p className="text-xs text-muted-foreground">
                            Dernières 4 semaines
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Detailed Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                    <CardHeader>
                        <CardTitle>Répartition par priorité</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                        <div className="flex items-center justify-between">
                            <span className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                                Priorité élevée
                            </span>
                            <span className={`font-semibold ${getPriorityColor('high')}`}>
                                {stats.eventsByPriority.high}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                                Priorité moyenne
                            </span>
                            <span className={`font-semibold ${getPriorityColor('medium')}`}>
                                {stats.eventsByPriority.medium}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                                Priorité faible
                            </span>
                            <span className={`font-semibold ${getPriorityColor('low')}`}>
                                {stats.eventsByPriority.low}
                            </span>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Répartition par statut</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                        <div className="flex items-center justify-between">
                            <span className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                                Confirmés
                            </span>
                            <span className={`font-semibold ${getStatusColor('confirmed')}`}>
                                {stats.eventsByStatus.confirmed}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                                Provisoires
                            </span>
                            <span className={`font-semibold ${getStatusColor('tentative')}`}>
                                {stats.eventsByStatus.tentative}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                                Annulés
                            </span>
                            <span className={`font-semibold ${getStatusColor('cancelled')}`}>
                                {stats.eventsByStatus.cancelled}
                            </span>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Additional Insights */}
            <Card>
                <CardHeader>
                    <CardTitle>Insights</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                        <span>Jours chargés (plus de 2 événements)</span>
                        <span className="font-semibold">{stats.busyDays}</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                        <span>Événements ce mois-ci</span>
                        <span className="font-semibold">{stats.thisMonthEvents}</span>
                    </div>
                    {stats.totalEvents > 0 && (
                        <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                            <p className="text-sm text-blue-800 dark:text-blue-200">
                                {stats.upcomingEvents > 0 
                                    ? `Vous avez ${stats.upcomingEvents} événement${stats.upcomingEvents > 1 ? 's' : ''} à venir. Restez organisé !`
                                    : "Aucun événement à venir. C'est le moment parfait pour planifier !"
                                }
                            </p>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
