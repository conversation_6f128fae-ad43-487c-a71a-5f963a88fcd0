import { Head, Link, router } from '@inertiajs/react';
import { useState } from 'react';
import { Calendar, Clock, Car, User, Filter, Eye, Edit, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';

interface Appointment {
    id: number;
    scheduled_at: string;
    service_type: string;
    status: string;
    price: number;
    notes?: string;
    created_at: string;
    user: {
        id: number;
        name: string;
        email: string;
        phone?: string;
    };
    vehicle: {
        id: number;
        brand: string;
        model: string;
        license_plate: string;
        color: string;
    };
}

interface AppointmentsIndexProps {
    appointments: Appointment[];
    filters: {
        status?: string;
        date_from?: string;
        date_to?: string;
    };
}

export default function AppointmentsIndex({ appointments, filters }: AppointmentsIndexProps) {
    const [statusFilter, setStatusFilter] = useState(filters.status || 'all');

    const handleStatusFilter = (status: string) => {
        setStatusFilter(status);
        router.get('/admin/appointments', { status: status === 'all' ? '' : status }, {
            preserveState: true,
            replace: true,
        });
    };

    const handleStatusUpdate = (appointmentId: number, newStatus: string) => {
        router.patch(`/admin/appointments/${appointmentId}/status`, {
            status: newStatus
        }, {
            preserveScroll: true,
        });
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800';
            case 'in_progress':
                return 'bg-yellow-100 text-yellow-800';
            case 'pending':
                return 'bg-orange-100 text-orange-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed': return 'Terminé';
            case 'confirmed': return 'Confirmé';
            case 'in_progress': return 'En cours';
            case 'pending': return 'En attente';
            case 'cancelled': return 'Annulé';
            default: return status;
        }
    };

    const getServiceLabel = (serviceType: string) => {
        switch (serviceType) {
            case 'basic': return 'Lavage Basique';
            case 'premium': return 'Lavage Premium';
            case 'deluxe': return 'Lavage Deluxe';
            default: return serviceType;
        }
    };

    return (
        <AppLayout>
            <Head title="Gestion des Rendez-vous - AutoWash Admin" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Gestion des Rendez-vous</h1>
                        <p className="text-gray-600">Gérez tous les rendez-vous de lavage ({appointments.length} rendez-vous)</p>
                    </div>
                </div>

                {/* Filtres */}
                <Card>
                    <CardContent className="p-4">
                        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                            <div className="flex items-center gap-2">
                                <Filter className="w-4 h-4 text-gray-500" />
                                <span className="text-sm font-medium text-gray-700">Filtres:</span>
                            </div>
                            <div className="flex flex-col sm:flex-row gap-3">
                                <div className="flex items-center gap-2">
                                    <label className="text-sm text-gray-600">Statut:</label>
                                    <Select value={statusFilter} onValueChange={handleStatusFilter}>
                                        <SelectTrigger className="w-40">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">Tous</SelectItem>
                                            <SelectItem value="pending">En attente</SelectItem>
                                            <SelectItem value="confirmed">Confirmé</SelectItem>
                                            <SelectItem value="in_progress">En cours</SelectItem>
                                            <SelectItem value="completed">Terminé</SelectItem>
                                            <SelectItem value="cancelled">Annulé</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Liste des rendez-vous */}
                {appointments.length > 0 ? (
                    <div className="space-y-4">
                        {appointments.map((appointment) => (
                            <Card key={appointment.id} className="hover:shadow-md transition-shadow">
                                <CardContent className="p-6">
                                    <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
                                        {/* Informations du client et véhicule */}
                                        <div className="lg:col-span-2">
                                            <div className="flex items-start gap-3">
                                                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <User className="w-5 h-5 text-blue-600" />
                                                </div>
                                                <div className="flex-1">
                                                    <h3 className="font-semibold text-base">
                                                        {appointment.user.name}
                                                    </h3>
                                                    <p className="text-sm text-gray-600">
                                                        {appointment.user.email}
                                                    </p>
                                                    {appointment.user.phone && (
                                                        <p className="text-sm text-gray-600">
                                                            {appointment.user.phone}
                                                        </p>
                                                    )}
                                                    <div className="flex items-center gap-2 mt-2">
                                                        <Car className="w-4 h-4 text-gray-400" />
                                                        <span className="text-sm font-medium">
                                                            {appointment.vehicle.brand} {appointment.vehicle.model}
                                                        </span>
                                                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                                                            {appointment.vehicle.license_plate}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Détails du rendez-vous */}
                                        <div>
                                            <div className="space-y-2">
                                                <div className="flex items-center gap-2">
                                                    <Calendar className="w-4 h-4 text-gray-400" />
                                                    <span className="text-sm">
                                                        {new Date(appointment.scheduled_at).toLocaleDateString('fr-FR')}
                                                    </span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Clock className="w-4 h-4 text-gray-400" />
                                                    <span className="text-sm">
                                                        {new Date(appointment.scheduled_at).toLocaleTimeString('fr-FR', { 
                                                            hour: '2-digit', 
                                                            minute: '2-digit' 
                                                        })}
                                                    </span>
                                                </div>
                                                <div className="text-sm">
                                                    <span className="font-medium">{getServiceLabel(appointment.service_type)}</span>
                                                </div>
                                                <div className="text-lg font-bold text-green-600">
                                                    {appointment.price} DH
                                                </div>
                                            </div>
                                        </div>

                                        {/* Actions et statut */}
                                        <div className="flex flex-col gap-3">
                                            <Badge className={getStatusColor(appointment.status)}>
                                                {getStatusText(appointment.status)}
                                            </Badge>
                                            
                                            <div className="flex flex-col gap-2">
                                                <Button asChild size="sm" variant="outline" className="w-full">
                                                    <Link href={`/admin/appointments/${appointment.id}`}>
                                                        <Eye className="w-4 h-4 mr-1" />
                                                        Voir
                                                    </Link>
                                                </Button>
                                                
                                                {appointment.status === 'pending' && (
                                                    <Button 
                                                        size="sm" 
                                                        onClick={() => handleStatusUpdate(appointment.id, 'confirmed')}
                                                        className="w-full"
                                                    >
                                                        <CheckCircle className="w-4 h-4 mr-1" />
                                                        Confirmer
                                                    </Button>
                                                )}
                                                
                                                {appointment.status === 'confirmed' && (
                                                    <Button 
                                                        size="sm" 
                                                        onClick={() => handleStatusUpdate(appointment.id, 'in_progress')}
                                                        className="w-full bg-yellow-600 hover:bg-yellow-700"
                                                    >
                                                        <AlertCircle className="w-4 h-4 mr-1" />
                                                        Démarrer
                                                    </Button>
                                                )}
                                                
                                                {appointment.status === 'in_progress' && (
                                                    <Button 
                                                        size="sm" 
                                                        onClick={() => handleStatusUpdate(appointment.id, 'completed')}
                                                        className="w-full bg-green-600 hover:bg-green-700"
                                                    >
                                                        <CheckCircle className="w-4 h-4 mr-1" />
                                                        Terminer
                                                    </Button>
                                                )}
                                                
                                                {['pending', 'confirmed'].includes(appointment.status) && (
                                                    <Button 
                                                        size="sm" 
                                                        variant="outline"
                                                        onClick={() => handleStatusUpdate(appointment.id, 'cancelled')}
                                                        className="w-full text-red-600 hover:text-red-700"
                                                    >
                                                        <XCircle className="w-4 h-4 mr-1" />
                                                        Annuler
                                                    </Button>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun rendez-vous</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Aucun rendez-vous ne correspond aux filtres sélectionnés.
                            </p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
