<?php

namespace App\Http\Controllers;

use App\Models\Event;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class EventController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Auth::user()->events()->with(['calendar', 'category']);

        // Filter by date range if provided
        if ($request->has('start') && $request->has('end')) {
            $start = Carbon::parse($request->start)->startOfDay();
            $end = Carbon::parse($request->end)->endOfDay();
            $query->betweenDates($start, $end);
        }

        // Filter by calendar if provided
        if ($request->has('calendar_id')) {
            $query->where('calendar_id', $request->calendar_id);
        }

        // Filter by category if provided
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        $events = $query->orderBy('start_date')->get();

        return Inertia::render('Events/Index', [
            'events' => $events,
            'filters' => $request->only(['start', 'end', 'calendar_id', 'category_id']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $calendars = Auth::user()->calendars()->get(['id', 'name', 'color']);
        $categories = Auth::user()->categories()->get(['id', 'name', 'color']);

        return Inertia::render('Events/Create', [
            'calendars' => $calendars,
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:2000',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'all_day' => 'boolean',
            'location' => 'nullable|string|max:255',
            'status' => 'in:confirmed,tentative,cancelled',
            'priority' => 'in:low,medium,high',
            'calendar_id' => 'required|exists:calendars,id',
            'category_id' => 'nullable|exists:categories,id',
            'recurrence_type' => 'in:none,daily,weekly,monthly,yearly',
            'recurrence_interval' => 'integer|min:1|max:365',
            'recurrence_days' => 'nullable|array',
            'recurrence_end_date' => 'nullable|date|after:start_date',
            'recurrence_count' => 'nullable|integer|min:1|max:1000',
        ]);

        // Verify calendar belongs to user
        $calendar = Auth::user()->calendars()->findOrFail($validated['calendar_id']);
        
        // Verify category belongs to user if provided
        if (isset($validated['category_id'])) {
            Auth::user()->categories()->findOrFail($validated['category_id']);
        }

        $event = Auth::user()->events()->create($validated);

        return redirect()->route('calendar.index')
            ->with('success', 'Event created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Event $event)
    {
        $this->authorize('view', $event);

        return Inertia::render('Events/Show', [
            'event' => $event->load(['calendar', 'category']),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Event $event)
    {
        $this->authorize('update', $event);

        $calendars = Auth::user()->calendars()->get(['id', 'name', 'color']);
        $categories = Auth::user()->categories()->get(['id', 'name', 'color']);

        return Inertia::render('Events/Edit', [
            'event' => $event,
            'calendars' => $calendars,
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Event $event)
    {
        $this->authorize('update', $event);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:2000',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'all_day' => 'boolean',
            'location' => 'nullable|string|max:255',
            'status' => 'in:confirmed,tentative,cancelled',
            'priority' => 'in:low,medium,high',
            'calendar_id' => 'required|exists:calendars,id',
            'category_id' => 'nullable|exists:categories,id',
            'recurrence_type' => 'in:none,daily,weekly,monthly,yearly',
            'recurrence_interval' => 'integer|min:1|max:365',
            'recurrence_days' => 'nullable|array',
            'recurrence_end_date' => 'nullable|date|after:start_date',
            'recurrence_count' => 'nullable|integer|min:1|max:1000',
        ]);

        // Verify calendar belongs to user
        Auth::user()->calendars()->findOrFail($validated['calendar_id']);
        
        // Verify category belongs to user if provided
        if (isset($validated['category_id'])) {
            Auth::user()->categories()->findOrFail($validated['category_id']);
        }

        $event->update($validated);

        return redirect()->route('calendar.index')
            ->with('success', 'Event updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Event $event)
    {
        $this->authorize('delete', $event);

        $event->delete();

        return redirect()->route('calendar.index')
            ->with('success', 'Event deleted successfully.');
    }

    /**
     * Get events for calendar API requests.
     */
    public function api(Request $request)
    {
        $query = Auth::user()->events()->with(['calendar', 'category']);

        // Filter by date range
        if ($request->has('start') && $request->has('end')) {
            $start = Carbon::parse($request->start)->startOfDay();
            $end = Carbon::parse($request->end)->endOfDay();
            $query->betweenDates($start, $end);
        }

        $events = $query->get()->map(function ($event) {
            return [
                'id' => $event->id,
                'title' => $event->title,
                'start' => $event->start_date->toISOString(),
                'end' => $event->end_date->toISOString(),
                'allDay' => $event->all_day,
                'backgroundColor' => $event->calendar->color,
                'borderColor' => $event->calendar->color,
                'extendedProps' => [
                    'description' => $event->description,
                    'location' => $event->location,
                    'status' => $event->status,
                    'priority' => $event->priority,
                    'calendar' => $event->calendar->name,
                    'category' => $event->category?->name,
                ],
            ];
        });

        return response()->json($events);
    }
}
