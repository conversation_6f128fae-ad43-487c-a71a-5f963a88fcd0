import { Head, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { Users, Search, Eye, Car, Calendar, Phone, Mail } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
    created_at: string;
    vehicles_count: number;
    appointments_count: number;
    appointments: Array<{
        scheduled_at: string;
        status: string;
    }>;
}

interface UsersIndexProps {
    users: {
        data: User[];
        links: any[];
        meta: any;
    };
    filters: {
        search?: string;
    };
}

export default function UsersIndex({ users, filters }: UsersIndexProps) {
    const [search, setSearch] = useState(filters.search || '');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/admin/users', { search }, {
            preserveState: true,
            replace: true,
        });
    };

    const getLastAppointment = (appointments: User['appointments']) => {
        if (!appointments || appointments.length === 0) return null;
        return appointments[0];
    };

    return (
        <AppLayout>
            <Head title="Gestion des Clients - AutoWash Admin" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Gestion des Clients</h1>
                        <p className="text-gray-600">Gérez tous les clients ({users.meta.total} clients)</p>
                    </div>
                </div>

                {/* Search */}
                <Card>
                    <CardContent className="p-4">
                        <form onSubmit={handleSearch} className="flex gap-4">
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                    <Input
                                        type="text"
                                        placeholder="Rechercher par nom, email ou téléphone..."
                                        value={search}
                                        onChange={(e) => setSearch(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>
                            <Button type="submit">Rechercher</Button>
                            {filters.search && (
                                <Button 
                                    type="button" 
                                    variant="outline"
                                    onClick={() => {
                                        setSearch('');
                                        router.get('/admin/users');
                                    }}
                                >
                                    Effacer
                                </Button>
                            )}
                        </form>
                    </CardContent>
                </Card>

                {/* Liste des clients */}
                {users.data.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {users.data.map((user) => {
                            const lastAppointment = getLastAppointment(user.appointments);
                            
                            return (
                                <Card key={user.id} className="hover:shadow-md transition-shadow">
                                    <CardHeader className="pb-3">
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <Users className="w-5 h-5 text-blue-600" />
                                                </div>
                                                <div>
                                                    <CardTitle className="text-base">{user.name}</CardTitle>
                                                    <p className="text-sm text-gray-600">
                                                        Client depuis {new Date(user.created_at).toLocaleDateString('fr-FR')}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        {/* Contact */}
                                        <div className="space-y-2">
                                            <div className="flex items-center gap-2 text-sm">
                                                <Mail className="w-4 h-4 text-gray-400" />
                                                <span className="truncate">{user.email}</span>
                                            </div>
                                            {user.phone && (
                                                <div className="flex items-center gap-2 text-sm">
                                                    <Phone className="w-4 h-4 text-gray-400" />
                                                    <span>{user.phone}</span>
                                                </div>
                                            )}
                                        </div>

                                        {/* Statistiques */}
                                        <div className="grid grid-cols-2 gap-4 pt-3 border-t">
                                            <div className="text-center">
                                                <div className="flex items-center justify-center gap-1">
                                                    <Car className="w-4 h-4 text-gray-400" />
                                                    <span className="text-lg font-bold text-blue-600">
                                                        {user.vehicles_count}
                                                    </span>
                                                </div>
                                                <p className="text-xs text-gray-600">Véhicule{user.vehicles_count > 1 ? 's' : ''}</p>
                                            </div>
                                            <div className="text-center">
                                                <div className="flex items-center justify-center gap-1">
                                                    <Calendar className="w-4 h-4 text-gray-400" />
                                                    <span className="text-lg font-bold text-green-600">
                                                        {user.appointments_count}
                                                    </span>
                                                </div>
                                                <p className="text-xs text-gray-600">RDV</p>
                                            </div>
                                        </div>

                                        {/* Dernier rendez-vous */}
                                        {lastAppointment && (
                                            <div className="pt-3 border-t">
                                                <p className="text-xs text-gray-500 mb-1">Dernier rendez-vous:</p>
                                                <p className="text-sm">
                                                    {new Date(lastAppointment.scheduled_at).toLocaleDateString('fr-FR')}
                                                </p>
                                            </div>
                                        )}

                                        {/* Actions */}
                                        <div className="pt-3">
                                            <Button asChild size="sm" className="w-full">
                                                <Link href={`/admin/users/${user.id}`}>
                                                    <Eye className="w-4 h-4 mr-1" />
                                                    Voir le profil
                                                </Link>
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            );
                        })}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Users className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun client trouvé</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                {filters.search 
                                    ? 'Aucun client ne correspond à votre recherche.'
                                    : 'Aucun client enregistré pour le moment.'
                                }
                            </p>
                        </CardContent>
                    </Card>
                )}

                {/* Pagination */}
                {users.links && users.links.length > 3 && (
                    <div className="flex justify-center">
                        <div className="flex gap-2">
                            {users.links.map((link, index) => (
                                <Button
                                    key={index}
                                    variant={link.active ? "default" : "outline"}
                                    size="sm"
                                    disabled={!link.url}
                                    onClick={() => link.url && router.get(link.url)}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
