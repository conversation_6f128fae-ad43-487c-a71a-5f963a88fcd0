<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class OptimizeCalendarQueries
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Enable query optimization for calendar-related requests
        if ($this->isCalendarRequest($request)) {
            // Enable query log for debugging in development
            if (app()->environment('local')) {
                DB::enableQueryLog();
            }

            // Set memory limit for large calendar operations
            ini_set('memory_limit', '256M');

            // Set execution time limit for complex recurring event calculations
            set_time_limit(60);
        }

        $response = $next($request);

        // Log slow queries in development
        if (app()->environment('local') && $this->isCalendarRequest($request)) {
            $queries = DB::getQueryLog();
            $slowQueries = array_filter($queries, function ($query) {
                return $query['time'] > 100; // Log queries taking more than 100ms
            });

            if (!empty($slowQueries)) {
                logger()->warning('Slow calendar queries detected', [
                    'url' => $request->url(),
                    'queries' => $slowQueries,
                ]);
            }
        }

        return $response;
    }

    /**
     * Determine if the request is calendar-related.
     */
    private function isCalendarRequest(Request $request): bool
    {
        $calendarPaths = [
            'calendar',
            'events',
            'calendars',
            'categories',
            'api/events',
            'api/calendars',
            'api/categories',
        ];

        $path = $request->path();

        foreach ($calendarPaths as $calendarPath) {
            if (str_starts_with($path, $calendarPath)) {
                return true;
            }
        }

        return false;
    }
}
