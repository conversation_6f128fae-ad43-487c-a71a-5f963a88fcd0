<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SettingsController extends Controller
{
    /**
     * Display settings page.
     */
    public function index()
    {
        $settings = [
            'business_info' => [
                'name' => 'AutoWash',
                'address' => 'Casablanca, Maroc',
                'phone' => '+212 123 456 789',
                'email' => '<EMAIL>',
                'hours' => [
                    'monday' => '08:00-18:00',
                    'tuesday' => '08:00-18:00',
                    'wednesday' => '08:00-18:00',
                    'thursday' => '08:00-18:00',
                    'friday' => '08:00-18:00',
                    'saturday' => '08:00-18:00',
                    'sunday' => 'Fermé',
                ],
            ],
            'services' => [
                'basic' => [
                    'name' => 'Lavage Basique',
                    'price' => 150,
                    'duration' => 30,
                    'description' => 'Lavage extérieur + aspirateur',
                ],
                'premium' => [
                    'name' => 'Lavage Premium',
                    'price' => 250,
                    'duration' => 45,
                    'description' => 'Lavage complet + cire + pneus',
                ],
                'deluxe' => [
                    'name' => 'Lavage Deluxe',
                    'price' => 350,
                    'duration' => 60,
                    'description' => 'Lavage premium + intérieur + détailing',
                ],
            ],
            'appointment_settings' => [
                'advance_booking_days' => 30,
                'cancellation_hours' => 24,
                'confirmation_required' => true,
                'auto_confirm_after_hours' => 2,
            ],
        ];

        return Inertia::render('Admin/Settings/Index', [
            'settings' => $settings,
        ]);
    }

    /**
     * Update settings.
     */
    public function update(Request $request)
    {
        // Ici vous pourriez sauvegarder les paramètres en base de données
        // Pour l'instant, on simule juste une mise à jour réussie
        
        return redirect()->route('admin.settings.index')
            ->with('success', 'Paramètres mis à jour avec succès !');
    }
}
