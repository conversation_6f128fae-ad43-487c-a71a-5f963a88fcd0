import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { type Calendar, type CalendarEvent, type Category } from '@/types';
import { Link } from '@inertiajs/react';
import { Calendar as CalendarIcon, Search, X } from 'lucide-react';
import { useEffect, useState } from 'react';

interface EventSearchProps {
    calendars: Calendar[];
    categories: Category[];
}

export function EventSearch({ calendars, categories }: EventSearchProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCalendar, setSelectedCalendar] = useState<string>('all');
    const [selectedCategory, setSelectedCategory] = useState<string>('all');
    const [searchResults, setSearchResults] = useState<CalendarEvent[]>([]);
    const [loading, setLoading] = useState(false);
    const [hasSearched, setHasSearched] = useState(false);

    const handleSearch = async () => {
        if (!searchTerm.trim() && selectedCalendar === 'all' && selectedCategory === 'all') {
            return;
        }

        setLoading(true);
        setHasSearched(true);

        try {
            const params = new URLSearchParams();

            // Add date range for the next year to get comprehensive results
            const today = new Date();
            const nextYear = new Date();
            nextYear.setFullYear(today.getFullYear() + 1);

            params.append('start', today.toISOString().split('T')[0]);
            params.append('end', nextYear.toISOString().split('T')[0]);

            if (selectedCalendar && selectedCalendar !== 'all') {
                params.append('calendar_id', selectedCalendar);
            }

            if (selectedCategory && selectedCategory !== 'all') {
                params.append('category_id', selectedCategory);
            }

            const response = await fetch(`/api/events?${params}`);
            let events = await response.json();

            // Filter by search term on the frontend
            if (searchTerm.trim()) {
                const term = searchTerm.toLowerCase();
                events = events.filter((event: CalendarEvent) =>
                    event.title.toLowerCase().includes(term) ||
                    event.extendedProps.description?.toLowerCase().includes(term) ||
                    event.extendedProps.location?.toLowerCase().includes(term)
                );
            }

            setSearchResults(events);
        } catch (error) {
            console.error('Search failed:', error);
            setSearchResults([]);
        } finally {
            setLoading(false);
        }
    };

    const clearSearch = () => {
        setSearchTerm('');
        setSelectedCalendar('all');
        setSelectedCategory('all');
        setSearchResults([]);
        setHasSearched(false);
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            handleSearch();
        }
    };

    return (
        <div className="space-y-6">
            {/* Search Form */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Search className="h-5 w-5" />
                        Rechercher des événements
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <Input
                                placeholder="Rechercher par titre, description ou lieu..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                onKeyPress={handleKeyPress}
                            />
                        </div>

                        <div>
                            <Select value={selectedCalendar} onValueChange={setSelectedCalendar}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Tous les calendriers" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Tous les calendriers</SelectItem>
                                    {calendars.map((calendar) => (
                                        <SelectItem key={calendar.id} value={calendar.id.toString()}>
                                            <div className="flex items-center gap-2">
                                                <div
                                                    className="w-3 h-3 rounded-full"
                                                    style={{ backgroundColor: calendar.color }}
                                                />
                                                {calendar.name}
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Toutes les catégories" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Toutes les catégories</SelectItem>
                                    {categories.map((category) => (
                                        <SelectItem key={category.id} value={category.id.toString()}>
                                            <div className="flex items-center gap-2">
                                                <div
                                                    className="w-3 h-3 rounded-full"
                                                    style={{ backgroundColor: category.color }}
                                                />
                                                {category.name}
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="flex gap-2">
                        <Button onClick={handleSearch} disabled={loading}>
                            <Search className="h-4 w-4 mr-2" />
                            {loading ? 'Recherche...' : 'Rechercher'}
                        </Button>

                        {(searchTerm || selectedCalendar !== 'all' || selectedCategory !== 'all' || hasSearched) && (
                            <Button variant="outline" onClick={clearSearch}>
                                <X className="h-4 w-4 mr-2" />
                                Effacer
                            </Button>
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Search Results */}
            {hasSearched && (
                <Card>
                    <CardHeader>
                        <CardTitle>
                            Résultats de recherche ({searchResults.length})
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {loading ? (
                            <div className="text-center py-8">
                                <div className="text-muted-foreground">Recherche en cours...</div>
                            </div>
                        ) : searchResults.length === 0 ? (
                            <div className="text-center py-8">
                                <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <p className="text-muted-foreground">Aucun événement trouvé</p>
                                <p className="text-sm text-muted-foreground">
                                    Essayez de modifier vos critères de recherche
                                </p>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {searchResults.map((event) => (
                                    <div
                                        key={event.id}
                                        className="p-4 rounded-lg border border-border hover:bg-muted/50 transition-colors"
                                    >
                                        <div className="flex items-start justify-between">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-2 mb-2">
                                                    <div
                                                        className="w-3 h-3 rounded-full"
                                                        style={{ backgroundColor: event.backgroundColor }}
                                                    />
                                                    <h3 className="font-semibold">{event.title}</h3>
                                                    {event.extendedProps.priority === 'high' && (
                                                        <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                                                            Priorité élevée
                                                        </span>
                                                    )}
                                                </div>

                                                <div className="space-y-1 text-sm text-muted-foreground">
                                                    <div className="flex items-center gap-2">
                                                        <CalendarIcon className="h-4 w-4" />
                                                        <span>
                                                            {new Date(event.start).toLocaleDateString('fr-FR', {
                                                                weekday: 'long',
                                                                year: 'numeric',
                                                                month: 'long',
                                                                day: 'numeric'
                                                            })}
                                                        </span>
                                                        {!event.allDay && (
                                                            <span>
                                                                à {new Date(event.start).toLocaleTimeString('fr-FR', {
                                                                    hour: '2-digit',
                                                                    minute: '2-digit'
                                                                })}
                                                            </span>
                                                        )}
                                                    </div>

                                                    {event.extendedProps.location && (
                                                        <div>📍 {event.extendedProps.location}</div>
                                                    )}

                                                    {event.extendedProps.description && (
                                                        <div className="mt-2">
                                                            {event.extendedProps.description.length > 100
                                                                ? `${event.extendedProps.description.substring(0, 100)}...`
                                                                : event.extendedProps.description
                                                            }
                                                        </div>
                                                    )}

                                                    <div className="flex items-center gap-4 mt-2">
                                                        <span className="text-xs">
                                                            📅 {event.extendedProps.calendar}
                                                        </span>
                                                        {event.extendedProps.category && (
                                                            <span className="text-xs">
                                                                🏷️ {event.extendedProps.category}
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex gap-2">
                                                <Button size="sm" variant="outline" asChild>
                                                    <Link href={`/events/${event.id}`}>
                                                        Voir
                                                    </Link>
                                                </Button>
                                                <Button size="sm" variant="outline" asChild>
                                                    <Link href={`/events/${event.id}/edit`}>
                                                        Modifier
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
