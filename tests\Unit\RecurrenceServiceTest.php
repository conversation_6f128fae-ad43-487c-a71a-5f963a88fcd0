<?php

namespace Tests\Unit;

use App\Models\Calendar;
use App\Models\Event;
use App\Models\User;
use App\Services\RecurrenceService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RecurrenceServiceTest extends TestCase
{
    use RefreshDatabase;

    private RecurrenceService $recurrenceService;
    private User $user;
    private Calendar $calendar;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->recurrenceService = new RecurrenceService();
        $this->user = User::factory()->create();
        $this->calendar = Calendar::factory()->create(['user_id' => $this->user->id]);
    }

    public function test_generates_daily_recurring_events()
    {
        $event = Event::factory()->create([
            'user_id' => $this->user->id,
            'calendar_id' => $this->calendar->id,
            'start_date' => Carbon::parse('2024-01-01 10:00:00'),
            'end_date' => Carbon::parse('2024-01-01 11:00:00'),
            'recurrence_type' => 'daily',
            'recurrence_interval' => 1,
            'recurrence_end_date' => Carbon::parse('2024-01-05'),
        ]);

        $startDate = Carbon::parse('2024-01-01');
        $endDate = Carbon::parse('2024-01-10');

        $instances = $this->recurrenceService->generateRecurringEvents($event, $startDate, $endDate);

        // Should generate 5 instances (Jan 1-5)
        $this->assertCount(5, $instances);
        
        // Check first instance
        $this->assertEquals('2024-01-01T10:00:00.000000Z', $instances[0]['start']);
        $this->assertEquals('2024-01-01T11:00:00.000000Z', $instances[0]['end']);
        
        // Check last instance
        $this->assertEquals('2024-01-05T10:00:00.000000Z', $instances[4]['start']);
        $this->assertEquals('2024-01-05T11:00:00.000000Z', $instances[4]['end']);
    }

    public function test_generates_weekly_recurring_events()
    {
        $event = Event::factory()->create([
            'user_id' => $this->user->id,
            'calendar_id' => $this->calendar->id,
            'start_date' => Carbon::parse('2024-01-01 10:00:00'), // Monday
            'end_date' => Carbon::parse('2024-01-01 11:00:00'),
            'recurrence_type' => 'weekly',
            'recurrence_interval' => 1,
            'recurrence_end_date' => Carbon::parse('2024-01-22'),
        ]);

        $startDate = Carbon::parse('2024-01-01');
        $endDate = Carbon::parse('2024-01-31');

        $instances = $this->recurrenceService->generateRecurringEvents($event, $startDate, $endDate);

        // Should generate 4 instances (every Monday for 3 weeks + original)
        $this->assertCount(4, $instances);
        
        // Check that all instances are on Mondays
        foreach ($instances as $instance) {
            $date = Carbon::parse($instance['start']);
            $this->assertEquals(1, $date->dayOfWeek); // Monday
        }
    }

    public function test_generates_monthly_recurring_events()
    {
        $event = Event::factory()->create([
            'user_id' => $this->user->id,
            'calendar_id' => $this->calendar->id,
            'start_date' => Carbon::parse('2024-01-15 10:00:00'),
            'end_date' => Carbon::parse('2024-01-15 11:00:00'),
            'recurrence_type' => 'monthly',
            'recurrence_interval' => 1,
            'recurrence_end_date' => Carbon::parse('2024-04-15'),
        ]);

        $startDate = Carbon::parse('2024-01-01');
        $endDate = Carbon::parse('2024-05-01');

        $instances = $this->recurrenceService->generateRecurringEvents($event, $startDate, $endDate);

        // Should generate 4 instances (Jan, Feb, Mar, Apr)
        $this->assertCount(4, $instances);
        
        // Check that all instances are on the 15th
        foreach ($instances as $instance) {
            $date = Carbon::parse($instance['start']);
            $this->assertEquals(15, $date->day);
        }
    }

    public function test_respects_recurrence_count_limit()
    {
        $event = Event::factory()->create([
            'user_id' => $this->user->id,
            'calendar_id' => $this->calendar->id,
            'start_date' => Carbon::parse('2024-01-01 10:00:00'),
            'end_date' => Carbon::parse('2024-01-01 11:00:00'),
            'recurrence_type' => 'daily',
            'recurrence_interval' => 1,
            'recurrence_count' => 3,
            'recurrence_end_date' => null, // No end date, only count
        ]);

        $startDate = Carbon::parse('2024-01-01');
        $endDate = Carbon::parse('2024-01-31');

        $instances = $this->recurrenceService->generateRecurringEvents($event, $startDate, $endDate);

        // Should generate exactly 3 instances
        $this->assertCount(3, $instances);
    }

    public function test_does_not_generate_for_non_recurring_events()
    {
        $event = Event::factory()->create([
            'user_id' => $this->user->id,
            'calendar_id' => $this->calendar->id,
            'recurrence_type' => 'none',
        ]);

        $startDate = Carbon::parse('2024-01-01');
        $endDate = Carbon::parse('2024-01-31');

        $instances = $this->recurrenceService->generateRecurringEvents($event, $startDate, $endDate);

        $this->assertEmpty($instances);
    }

    public function test_should_generate_recurring_instances()
    {
        $recurringEvent = Event::factory()->create([
            'recurrence_type' => 'weekly',
            'recurrence_interval' => 1,
        ]);

        $nonRecurringEvent = Event::factory()->create([
            'recurrence_type' => 'none',
        ]);

        $this->assertTrue($this->recurrenceService->shouldGenerateRecurringInstances($recurringEvent));
        $this->assertFalse($this->recurrenceService->shouldGenerateRecurringInstances($nonRecurringEvent));
    }

    public function test_get_event_instances_includes_original_and_recurring()
    {
        $event = Event::factory()->create([
            'user_id' => $this->user->id,
            'calendar_id' => $this->calendar->id,
            'start_date' => Carbon::parse('2024-01-01 10:00:00'),
            'end_date' => Carbon::parse('2024-01-01 11:00:00'),
            'recurrence_type' => 'weekly',
            'recurrence_interval' => 1,
            'recurrence_end_date' => Carbon::parse('2024-01-15'),
        ]);

        $startDate = Carbon::parse('2024-01-01');
        $endDate = Carbon::parse('2024-01-31');

        $instances = $this->recurrenceService->getEventInstances($event, $startDate, $endDate);

        // Should include original event + recurring instances
        $this->assertGreaterThan(1, count($instances));
        
        // First instance should be the original event
        $this->assertEquals($event->id, $instances[0]['id']);
        $this->assertFalse($instances[0]['extendedProps']['isRecurring']);
        
        // Subsequent instances should be recurring
        if (count($instances) > 1) {
            $this->assertTrue($instances[1]['extendedProps']['isRecurring']);
            $this->assertEquals($event->id, $instances[1]['extendedProps']['originalEventId']);
        }
    }
}
