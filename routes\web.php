<?php

use App\Http\Controllers\CalendarController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\EventController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Calendar routes
    Route::get('calendar', function () {
        return Inertia::render('Calendar/Index');
    })->name('calendar.index');

    // Search route
    Route::get('events/search', function () {
        return Inertia::render('Events/Search');
    })->name('events.search');

    // Resource routes
    Route::resource('calendars', CalendarController::class);
    Route::resource('categories', CategoryController::class);
    Route::resource('events', EventController::class);

    // API routes for AJAX requests
    Route::prefix('api')->group(function () {
        Route::get('calendars', [CalendarController::class, 'api'])->name('api.calendars');
        Route::get('categories', [CategoryController::class, 'api'])->name('api.categories');
        Route::get('events', [EventController::class, 'api'])->name('api.events');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
