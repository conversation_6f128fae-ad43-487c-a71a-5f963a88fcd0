import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, Receipt, User, Car, Calendar, Download, DollarSign, CheckCircle, XCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';

interface Invoice {
    id: number;
    invoice_number: string;
    subtotal: number;
    tax_rate: number;
    tax_amount: number;
    total_amount: number;
    status: string;
    due_date: string;
    paid_at?: string;
    notes?: string;
    created_at: string;
    appointment: {
        id: number;
        service_type: string;
        scheduled_at: string;
        price: number;
        duration: number;
        user: {
            id: number;
            name: string;
            email: string;
            phone?: string;
        };
        vehicle: {
            id: number;
            brand: string;
            model: string;
            year: number;
            color: string;
            license_plate: string;
            vehicle_type: string;
        };
    };
}

interface ShowInvoiceProps {
    invoice: Invoice;
}

export default function ShowInvoice({ invoice }: ShowInvoiceProps) {
    const handleStatusUpdate = (newStatus: string) => {
        router.patch(`/admin/invoices/${invoice.id}/status`, {
            status: newStatus
        });
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'paid': return 'bg-green-100 text-green-800';
            case 'pending': return 'bg-orange-100 text-orange-800';
            case 'overdue': return 'bg-red-100 text-red-800';
            case 'cancelled': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'paid': return 'Payée';
            case 'pending': return 'En attente';
            case 'overdue': return 'En retard';
            case 'cancelled': return 'Annulée';
            default: return status;
        }
    };

    const getServiceLabel = (serviceType: string) => {
        switch (serviceType) {
            case 'basic': return 'Lavage Basique';
            case 'premium': return 'Lavage Premium';
            case 'deluxe': return 'Lavage Deluxe';
            default: return serviceType;
        }
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('fr-FR').format(amount) + ' DH';
    };

    return (
        <AppLayout>
            <Head title={`Facture ${invoice.invoice_number} - AutoWash Admin`} />
            
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button asChild variant="outline" size="sm">
                        <Link href="/admin/invoices">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Retour
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Facture {invoice.invoice_number}</h1>
                        <p className="text-gray-600">Détails complets de la facture</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Informations de la facture */}
                    <Card className="lg:col-span-2">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Receipt className="h-5 w-5" />
                                Détails de la facture
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* En-tête de facture */}
                            <div className="border-b pb-4">
                                <div className="flex justify-between items-start">
                                    <div>
                                        <h2 className="text-2xl font-bold">AutoWash</h2>
                                        <p className="text-gray-600">Centre de lavage automobile</p>
                                        <p className="text-sm text-gray-500">Casablanca, Maroc</p>
                                        <p className="text-sm text-gray-500">+212 123 456 789</p>
                                    </div>
                                    <div className="text-right">
                                        <h3 className="text-xl font-bold">{invoice.invoice_number}</h3>
                                        <p className="text-sm text-gray-600">
                                            Date: {new Date(invoice.created_at).toLocaleDateString('fr-FR')}
                                        </p>
                                        <p className="text-sm text-gray-600">
                                            Échéance: {new Date(invoice.due_date).toLocaleDateString('fr-FR')}
                                        </p>
                                        <Badge className={getStatusColor(invoice.status)}>
                                            {getStatusText(invoice.status)}
                                        </Badge>
                                    </div>
                                </div>
                            </div>

                            {/* Informations client */}
                            <div>
                                <h4 className="font-medium mb-2">Facturé à:</h4>
                                <div className="bg-gray-50 p-4 rounded-lg">
                                    <p className="font-medium">{invoice.appointment.user.name}</p>
                                    <p className="text-sm text-gray-600">{invoice.appointment.user.email}</p>
                                    {invoice.appointment.user.phone && (
                                        <p className="text-sm text-gray-600">{invoice.appointment.user.phone}</p>
                                    )}
                                </div>
                            </div>

                            {/* Détails du service */}
                            <div>
                                <h4 className="font-medium mb-2">Service fourni:</h4>
                                <div className="bg-gray-50 p-4 rounded-lg">
                                    <div className="flex justify-between items-start">
                                        <div>
                                            <p className="font-medium">{getServiceLabel(invoice.appointment.service_type)}</p>
                                            <p className="text-sm text-gray-600">
                                                {invoice.appointment.vehicle.brand} {invoice.appointment.vehicle.model} ({invoice.appointment.vehicle.license_plate})
                                            </p>
                                            <p className="text-sm text-gray-600">
                                                Date: {new Date(invoice.appointment.scheduled_at).toLocaleDateString('fr-FR')} à{' '}
                                                {new Date(invoice.appointment.scheduled_at).toLocaleTimeString('fr-FR', { 
                                                    hour: '2-digit', 
                                                    minute: '2-digit' 
                                                })}
                                            </p>
                                            <p className="text-sm text-gray-600">
                                                Durée: {invoice.appointment.duration} minutes
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-medium">{formatCurrency(invoice.subtotal)}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Calculs */}
                            <div className="border-t pt-4">
                                <div className="space-y-2">
                                    <div className="flex justify-between">
                                        <span>Sous-total HT:</span>
                                        <span>{formatCurrency(invoice.subtotal)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>TVA ({invoice.tax_rate}%):</span>
                                        <span>{formatCurrency(invoice.tax_amount)}</span>
                                    </div>
                                    <div className="flex justify-between text-lg font-bold border-t pt-2">
                                        <span>Total TTC:</span>
                                        <span>{formatCurrency(invoice.total_amount)}</span>
                                    </div>
                                </div>
                            </div>

                            {/* Notes */}
                            {invoice.notes && (
                                <div className="border-t pt-4">
                                    <h4 className="font-medium mb-2">Notes:</h4>
                                    <p className="text-sm text-gray-700">{invoice.notes}</p>
                                </div>
                            )}

                            {/* Informations de paiement */}
                            {invoice.paid_at && (
                                <div className="border-t pt-4">
                                    <div className="bg-green-50 p-4 rounded-lg">
                                        <div className="flex items-center gap-2 text-green-800">
                                            <CheckCircle className="h-4 w-4" />
                                            <span className="font-medium">Facture payée</span>
                                        </div>
                                        <p className="text-sm text-green-700 mt-1">
                                            Payée le {new Date(invoice.paid_at).toLocaleDateString('fr-FR')} à{' '}
                                            {new Date(invoice.paid_at).toLocaleTimeString('fr-FR', { 
                                                hour: '2-digit', 
                                                minute: '2-digit' 
                                            })}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Actions</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <Button asChild className="w-full">
                                <Link href={`/admin/invoices/${invoice.id}/download`}>
                                    <Download className="mr-2 h-4 w-4" />
                                    Télécharger PDF
                                </Link>
                            </Button>

                            {invoice.status === 'pending' && (
                                <>
                                    <Button 
                                        onClick={() => handleStatusUpdate('paid')}
                                        className="w-full bg-green-600 hover:bg-green-700"
                                    >
                                        <CheckCircle className="mr-2 h-4 w-4" />
                                        Marquer comme payée
                                    </Button>
                                    <Button 
                                        onClick={() => handleStatusUpdate('overdue')}
                                        variant="outline"
                                        className="w-full text-red-600 hover:text-red-700"
                                    >
                                        <XCircle className="mr-2 h-4 w-4" />
                                        Marquer en retard
                                    </Button>
                                </>
                            )}

                            {invoice.status === 'overdue' && (
                                <Button 
                                    onClick={() => handleStatusUpdate('paid')}
                                    className="w-full bg-green-600 hover:bg-green-700"
                                >
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Marquer comme payée
                                </Button>
                            )}

                            {['pending', 'overdue'].includes(invoice.status) && (
                                <Button 
                                    onClick={() => handleStatusUpdate('cancelled')}
                                    variant="outline"
                                    className="w-full text-gray-600 hover:text-gray-700"
                                >
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Annuler la facture
                                </Button>
                            )}

                            <div className="border-t pt-4">
                                <h4 className="font-medium mb-2">Liens rapides</h4>
                                <div className="space-y-2">
                                    <Button asChild variant="outline" size="sm" className="w-full">
                                        <Link href={`/admin/appointments/${invoice.appointment.id}`}>
                                            <Calendar className="mr-2 h-4 w-4" />
                                            Voir le rendez-vous
                                        </Link>
                                    </Button>
                                    <Button asChild variant="outline" size="sm" className="w-full">
                                        <Link href={`/admin/users/${invoice.appointment.user.id}`}>
                                            <User className="mr-2 h-4 w-4" />
                                            Profil client
                                        </Link>
                                    </Button>
                                    <Button asChild variant="outline" size="sm" className="w-full">
                                        <Link href={`/admin/vehicles/${invoice.appointment.vehicle.id}`}>
                                            <Car className="mr-2 h-4 w-4" />
                                            Détails véhicule
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
