import { EventSearch } from '@/components/calendar/event-search';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type Calendar, type Category } from '@/types';
import { Head } from '@inertiajs/react';
import { useEffect, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Calendar',
        href: '/calendar',
    },
    {
        title: 'Recherche',
        href: '/events/search',
    },
];

export default function SearchEvents() {
    const [calendars, setCalendars] = useState<Calendar[]>([]);
    const [categories, setCategories] = useState<Category[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const [calendarsResponse, categoriesResponse] = await Promise.all([
                    fetch('/api/calendars'),
                    fetch('/api/categories'),
                ]);

                const calendarsData = await calendarsResponse.json();
                const categoriesData = await categoriesResponse.json();

                setCalendars(calendarsData.calendars || []);
                setCategories(categoriesData.categories || []);
            } catch (error) {
                console.error('Failed to fetch data:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    if (loading) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Recherche d'événements" />
                <div className="flex items-center justify-center h-96">
                    <div className="text-muted-foreground">Chargement...</div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Recherche d'événements" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <EventSearch 
                    calendars={calendars}
                    categories={categories}
                />
            </div>
        </AppLayout>
    );
}
