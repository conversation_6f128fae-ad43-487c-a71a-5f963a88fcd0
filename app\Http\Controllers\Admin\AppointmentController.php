<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AppointmentController extends Controller
{
    /**
     * Display a listing of all appointments.
     */
    public function index(Request $request)
    {
        $query = Appointment::with(['user', 'vehicle']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('scheduled_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('scheduled_at', '<=', $request->date_to);
        }

        // Search by client name or vehicle
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhereHas('vehicle', function ($q) use ($search) {
                $q->where('brand', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%")
                  ->orWhere('license_plate', 'like', "%{$search}%");
            });
        }

        $appointments = $query->latest('scheduled_at')->paginate(20);

        return Inertia::render('Admin/Appointments/Index', [
            'appointments' => $appointments,
            'filters' => $request->only(['status', 'date_from', 'date_to', 'search']),
            'statusOptions' => [
                Appointment::STATUS_PENDING => 'En attente',
                Appointment::STATUS_CONFIRMED => 'Confirmé',
                Appointment::STATUS_IN_PROGRESS => 'En cours',
                Appointment::STATUS_COMPLETED => 'Terminé',
                Appointment::STATUS_CANCELLED => 'Annulé',
            ],
        ]);
    }

    /**
     * Display the specified appointment.
     */
    public function show(Appointment $appointment)
    {
        $appointment->load(['user', 'vehicle', 'invoice']);

        return Inertia::render('Admin/Appointments/Show', [
            'appointment' => $appointment,
        ]);
    }

    /**
     * Update the appointment status.
     */
    public function updateStatus(Request $request, Appointment $appointment)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,confirmed,in_progress,completed,cancelled',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $appointment->update($validated);

        return redirect()->back()->with('success', 'Statut du rendez-vous mis à jour avec succès !');
    }

    /**
     * Show the form for creating a new appointment (admin can create for any client).
     */
    public function create()
    {
        $clients = User::where('role', 'client')->with('vehicles')->get();
        $vehicles = Vehicle::with('user')->get();

        return Inertia::render('Admin/Appointments/Create', [
            'clients' => $clients,
            'vehicles' => $vehicles,
            'serviceTypes' => Appointment::SERVICE_TYPES,
            'servicePrices' => Appointment::SERVICE_PRICES,
            'serviceDurations' => Appointment::SERVICE_DURATIONS,
        ]);
    }

    /**
     * Store a newly created appointment.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'vehicle_id' => 'required|exists:vehicles,id',
            'service_type' => 'required|in:' . implode(',', array_keys(Appointment::SERVICE_TYPES)),
            'scheduled_at' => 'required|date|after:now',
            'status' => 'required|in:pending,confirmed,in_progress,completed,cancelled',
            'notes' => 'nullable|string|max:1000',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        // Verify the vehicle belongs to the selected user
        $vehicle = Vehicle::where('id', $validated['vehicle_id'])
            ->where('user_id', $validated['user_id'])
            ->firstOrFail();

        // Set price and duration based on service type
        $validated['price'] = Appointment::SERVICE_PRICES[$validated['service_type']];
        $validated['duration'] = Appointment::SERVICE_DURATIONS[$validated['service_type']];

        Appointment::create($validated);

        return redirect()->route('admin.appointments.index')
            ->with('success', 'Rendez-vous créé avec succès !');
    }

    /**
     * Show the form for editing the specified appointment.
     */
    public function edit(Appointment $appointment)
    {
        $appointment->load(['user', 'vehicle']);
        $clients = User::where('role', 'client')->with('vehicles')->get();
        $vehicles = Vehicle::with('user')->get();

        return Inertia::render('Admin/Appointments/Edit', [
            'appointment' => $appointment,
            'clients' => $clients,
            'vehicles' => $vehicles,
            'serviceTypes' => Appointment::SERVICE_TYPES,
            'servicePrices' => Appointment::SERVICE_PRICES,
            'serviceDurations' => Appointment::SERVICE_DURATIONS,
        ]);
    }

    /**
     * Update the specified appointment.
     */
    public function update(Request $request, Appointment $appointment)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'vehicle_id' => 'required|exists:vehicles,id',
            'service_type' => 'required|in:' . implode(',', array_keys(Appointment::SERVICE_TYPES)),
            'scheduled_at' => 'required|date',
            'status' => 'required|in:pending,confirmed,in_progress,completed,cancelled',
            'notes' => 'nullable|string|max:1000',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        // Verify the vehicle belongs to the selected user
        $vehicle = Vehicle::where('id', $validated['vehicle_id'])
            ->where('user_id', $validated['user_id'])
            ->firstOrFail();

        // Update price and duration based on service type
        $validated['price'] = Appointment::SERVICE_PRICES[$validated['service_type']];
        $validated['duration'] = Appointment::SERVICE_DURATIONS[$validated['service_type']];

        $appointment->update($validated);

        return redirect()->route('admin.appointments.index')
            ->with('success', 'Rendez-vous modifié avec succès !');
    }

    /**
     * Remove the specified appointment.
     */
    public function destroy(Appointment $appointment)
    {
        $appointment->delete();

        return redirect()->route('admin.appointments.index')
            ->with('success', 'Rendez-vous supprimé avec succès !');
    }
}
