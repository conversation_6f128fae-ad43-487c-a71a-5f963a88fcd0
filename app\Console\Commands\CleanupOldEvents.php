<?php

namespace App\Console\Commands;

use App\Models\Event;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CleanupOldEvents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'smartcalendar:cleanup-events 
                            {--days=365 : Number of days to keep events}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old events to maintain database performance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $dryRun = $this->option('dry-run');
        
        $cutoffDate = Carbon::now()->subDays($days);
        
        $this->info("Looking for events older than {$days} days (before {$cutoffDate->format('Y-m-d')})...");
        
        $query = Event::where('end_date', '<', $cutoffDate)
                     ->where('status', '!=', 'confirmed') // Keep confirmed events longer
                     ->whereNull('recurrence_end_date'); // Don't delete recurring events
        
        $count = $query->count();
        
        if ($count === 0) {
            $this->info('No old events found to clean up.');
            return 0;
        }
        
        if ($dryRun) {
            $this->info("Would delete {$count} old events (dry run mode).");
            
            // Show some examples
            $examples = $query->limit(5)->get(['id', 'title', 'end_date', 'status']);
            $this->table(
                ['ID', 'Title', 'End Date', 'Status'],
                $examples->map(fn($event) => [
                    $event->id,
                    substr($event->title, 0, 30) . (strlen($event->title) > 30 ? '...' : ''),
                    $event->end_date->format('Y-m-d H:i'),
                    $event->status,
                ])
            );
            
            if ($count > 5) {
                $this->info("... and " . ($count - 5) . " more events.");
            }
            
            return 0;
        }
        
        if (!$this->confirm("Are you sure you want to delete {$count} old events?")) {
            $this->info('Operation cancelled.');
            return 0;
        }
        
        $deleted = $query->delete();
        
        $this->info("Successfully deleted {$deleted} old events.");
        
        return 0;
    }
}
