<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class InvoiceController extends Controller
{
    /**
     * Display a listing of invoices.
     */
    public function index(Request $request)
    {
        $query = Invoice::with(['appointment.user', 'appointment.vehicle'])
            ->latest();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('appointment.user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $invoices = $query->paginate(15)->withQueryString();

        // Calculate stats
        $stats = [
            'total_invoices' => Invoice::count(),
            'paid_invoices' => Invoice::where('status', 'paid')->count(),
            'pending_invoices' => Invoice::where('status', 'pending')->count(),
            'overdue_invoices' => Invoice::where('status', 'overdue')->count(),
            'total_amount' => Invoice::where('status', 'paid')->sum('total_amount'),
            'pending_amount' => Invoice::whereIn('status', ['pending', 'overdue'])->sum('total_amount'),
        ];

        return Inertia::render('Admin/Invoices/Index', [
            'invoices' => $invoices,
            'stats' => $stats,
            'filters' => $request->only(['status', 'date_from', 'date_to', 'search']),
        ]);
    }

    /**
     * Display the specified invoice.
     */
    public function show(Invoice $invoice)
    {
        $invoice->load(['appointment.user', 'appointment.vehicle']);

        return Inertia::render('Admin/Invoices/Show', [
            'invoice' => $invoice,
        ]);
    }

    /**
     * Generate invoice for completed appointment.
     */
    public function generate(Appointment $appointment)
    {
        // Check if appointment is completed
        if ($appointment->status !== 'completed') {
            return redirect()->back()->with('error', 'Seuls les rendez-vous terminés peuvent être facturés.');
        }

        // Check if invoice already exists
        if ($appointment->invoice) {
            return redirect()->route('admin.invoices.show', $appointment->invoice)
                ->with('info', 'Une facture existe déjà pour ce rendez-vous.');
        }

        // Generate invoice number
        $lastInvoice = Invoice::latest('id')->first();
        $invoiceNumber = 'INV-' . date('Y') . '-' . str_pad(($lastInvoice ? $lastInvoice->id + 1 : 1), 4, '0', STR_PAD_LEFT);

        // Create invoice
        $invoice = Invoice::create([
            'appointment_id' => $appointment->id,
            'invoice_number' => $invoiceNumber,
            'subtotal' => $appointment->price,
            'tax_rate' => 20, // 20% TVA
            'tax_amount' => $appointment->price * 0.20,
            'total_amount' => $appointment->price * 1.20,
            'status' => 'pending',
            'due_date' => now()->addDays(30),
        ]);

        return redirect()->route('admin.invoices.show', $invoice)
            ->with('success', 'Facture générée avec succès !');
    }

    /**
     * Update invoice status.
     */
    public function updateStatus(Request $request, Invoice $invoice)
    {
        $request->validate([
            'status' => 'required|in:pending,paid,overdue,cancelled'
        ]);

        $invoice->update([
            'status' => $request->status,
            'paid_at' => $request->status === 'paid' ? now() : null,
        ]);

        return redirect()->back()->with('success', 'Statut de la facture mis à jour !');
    }

    /**
     * Download invoice PDF.
     */
    public function download(Invoice $invoice)
    {
        // Ici vous pourriez générer un PDF avec une librairie comme DomPDF
        // Pour l'instant, on redirige vers la vue
        return redirect()->route('admin.invoices.show', $invoice);
    }
}
