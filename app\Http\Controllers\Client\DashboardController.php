<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    public function index(): Response
    {
        $user = Auth::user();
        
        // Statistiques du client
        $stats = [
            'total_vehicles' => Vehicle::where('user_id', $user->id)->count(),
            'total_appointments' => Appointment::where('user_id', $user->id)->count(),
            'pending_appointments' => Appointment::where('user_id', $user->id)
                ->where('status', 'pending')
                ->count(),
            'next_appointment' => Appointment::where('user_id', $user->id)
                ->where('status', '!=', 'completed')
                ->where('scheduled_at', '>', now())
                ->with(['vehicle'])
                ->orderBy('scheduled_at')
                ->first(),
        ];

        // Rendez-vous récents
        $recent_appointments = Appointment::where('user_id', $user->id)
            ->with(['vehicle'])
            ->orderBy('scheduled_at', 'desc')
            ->limit(5)
            ->get();

        return Inertia::render('Client/Dashboard', [
            'stats' => $stats,
            'recent_appointments' => $recent_appointments,
        ]);
    }
}
