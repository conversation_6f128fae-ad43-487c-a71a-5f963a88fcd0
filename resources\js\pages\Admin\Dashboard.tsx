import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Users, Car, Calendar, Clock, Euro, TrendingUp } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';

interface DashboardProps {
    stats: {
        total_clients: number;
        total_vehicles: number;
        total_appointments: number;
        pending_appointments: number;
        today_appointments: number;
        revenue_this_month: number;
    };
    recent_appointments: Array<{
        id: number;
        scheduled_at: string;
        service_type: string;
        status: string;
        user: {
            name: string;
            email: string;
        };
        vehicle: {
            brand: string;
            model: string;
            license_plate: string;
        };
    }>;
    upcoming_appointments: Array<{
        id: number;
        scheduled_at: string;
        service_type: string;
        status: string;
        user: {
            name: string;
        };
        vehicle: {
            brand: string;
            model: string;
        };
    }>;
}

export default function AdminDashboard({ stats, recent_appointments, upcoming_appointments }: DashboardProps) {
    return (
        <AppLayout>
            <Head title="Dashboard Admin - AutoWash" />
            
            <div className="space-y-6">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Dashboard Admin</h1>
                    <p className="text-gray-600">Vue d'ensemble de votre activité AutoWash</p>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Clients</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_clients}</div>
                            <p className="text-xs text-muted-foreground">
                                Clients enregistrés
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Véhicules</CardTitle>
                            <Car className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_vehicles}</div>
                            <p className="text-xs text-muted-foreground">
                                Véhicules enregistrés
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Rendez-vous</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_appointments}</div>
                            <p className="text-xs text-muted-foreground">
                                Total des rendez-vous
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">En attente</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.pending_appointments}</div>
                            <p className="text-xs text-muted-foreground">
                                Rendez-vous en attente
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Aujourd'hui</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.today_appointments}</div>
                            <p className="text-xs text-muted-foreground">
                                Rendez-vous du jour
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Revenus du mois</CardTitle>
                            <Euro className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.revenue_this_month}€</div>
                            <p className="text-xs text-muted-foreground">
                                Chiffre d'affaires mensuel
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Actions rapides</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Button asChild className="justify-start">
                                <Link href={route('admin.appointments.index')}>
                                    <Calendar className="mr-2 h-4 w-4" />
                                    Gérer les rendez-vous
                                </Link>
                            </Button>
                            <Button asChild className="justify-start" variant="outline">
                                <Link href={route('admin.appointments.create')}>
                                    <Calendar className="mr-2 h-4 w-4" />
                                    Nouveau rendez-vous
                                </Link>
                            </Button>
                            <Button asChild className="justify-start" variant="outline">
                                <Link href={route('admin.appointments.index', { status: 'pending' })}>
                                    <Clock className="mr-2 h-4 w-4" />
                                    Rendez-vous en attente
                                </Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Recent Appointments */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Rendez-vous récents</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recent_appointments.slice(0, 5).map((appointment) => (
                                    <div key={appointment.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div>
                                            <p className="font-medium">{appointment.user.name}</p>
                                            <p className="text-sm text-gray-600">
                                                {appointment.vehicle.brand} {appointment.vehicle.model}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {appointment.service_type}
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                appointment.status === 'completed' ? 'bg-green-100 text-green-800' :
                                                appointment.status === 'confirmed' ? 'bg-blue-100 text-blue-800' :
                                                appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-gray-100 text-gray-800'
                                            }`}>
                                                {appointment.status}
                                            </span>
                                        </div>
                                    </div>
                                ))}
                                <div className="text-center">
                                    <Button asChild variant="outline" size="sm">
                                        <Link href={route('admin.appointments.index')}>
                                            Voir tous
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Upcoming Appointments */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Rendez-vous à venir</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {upcoming_appointments.slice(0, 5).map((appointment) => (
                                    <div key={appointment.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div>
                                            <p className="font-medium">{appointment.user.name}</p>
                                            <p className="text-sm text-gray-600">
                                                {appointment.vehicle.brand} {appointment.vehicle.model}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {new Date(appointment.scheduled_at).toLocaleDateString('fr-FR')} à {new Date(appointment.scheduled_at).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                                            </p>
                                        </div>
                                        <Button asChild size="sm" variant="outline">
                                            <Link href={route('admin.appointments.show', appointment.id)}>
                                                Voir
                                            </Link>
                                        </Button>
                                    </div>
                                ))}
                                <div className="text-center">
                                    <Button asChild variant="outline" size="sm">
                                        <Link href={route('admin.appointments.index')}>
                                            Voir tous
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
