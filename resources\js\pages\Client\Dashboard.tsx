import { Head, <PERSON> } from '@inertiajs/react';
import { Car, Calendar, Clock, Plus, ArrowRight } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';

interface DashboardProps {
    stats?: {
        total_vehicles: number;
        total_appointments: number;
        pending_appointments: number;
        next_appointment?: {
            id: number;
            scheduled_at: string;
            service_type: string;
            vehicle: {
                brand: string;
                model: string;
            };
        };
    };
    recent_appointments?: Array<{
        id: number;
        scheduled_at: string;
        service_type: string;
        status: string;
        vehicle: {
            brand: string;
            model: string;
        };
    }>;
}

export default function ClientDashboard({ stats = {}, recent_appointments = [] }: DashboardProps) {
    // Données par défaut si pas de props
    const defaultStats = {
        total_vehicles: stats?.total_vehicles || 0,
        total_appointments: stats?.total_appointments || 0,
        pending_appointments: stats?.pending_appointments || 0,
        next_appointment: stats?.next_appointment || null,
    };

    return (
        <AppLayout>
            <Head title="Tableau de bord - AutoWash" />

            <div className="space-y-6">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Tableau de bord</h1>
                    <p className="text-gray-600">Gérez vos véhicules et rendez-vous de lavage</p>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Mes Véhicules</CardTitle>
                            <Car className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{defaultStats.total_vehicles}</div>
                            <p className="text-xs text-muted-foreground">
                                Véhicules enregistrés
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Rendez-vous</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{defaultStats.total_appointments}</div>
                            <p className="text-xs text-muted-foreground">
                                Total des rendez-vous
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">En attente</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{defaultStats.pending_appointments}</div>
                            <p className="text-xs text-muted-foreground">
                                Rendez-vous en attente
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Actions rapides</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <Button asChild className="w-full justify-start" variant="outline">
                                <Link href="/client/vehicles/create">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Ajouter un véhicule
                                </Link>
                            </Button>
                            <Button asChild className="w-full justify-start" variant="outline">
                                <Link href="/client/appointments/create">
                                    <Calendar className="mr-2 h-4 w-4" />
                                    Prendre rendez-vous
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Next Appointment */}
                    {defaultStats.next_appointment && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Prochain rendez-vous</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <p className="font-medium">
                                        {defaultStats.next_appointment?.vehicle?.brand} {defaultStats.next_appointment?.vehicle?.model}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        Service: {defaultStats.next_appointment?.service_type}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        Date: {defaultStats.next_appointment?.scheduled_at && new Date(defaultStats.next_appointment.scheduled_at).toLocaleDateString('fr-FR')}
                                    </p>
                                    <Button asChild size="sm" className="mt-2">
                                        <Link href={`/client/appointments/${defaultStats.next_appointment?.id}`}>
                                            Voir détails
                                            <ArrowRight className="ml-2 h-4 w-4" />
                                        </Link>
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>

                {/* Recent Appointments */}
                <Card>
                    <CardHeader>
                        <CardTitle>Rendez-vous récents</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {recent_appointments.length > 0 ? (
                            <div className="space-y-4">
                                {recent_appointments.map((appointment) => (
                                    <div key={appointment.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div>
                                            <p className="font-medium">
                                                {appointment.vehicle.brand} {appointment.vehicle.model}
                                            </p>
                                            <p className="text-sm text-gray-600">
                                                {appointment.service_type} - {new Date(appointment.scheduled_at).toLocaleDateString('fr-FR')}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                appointment.status === 'completed' ? 'bg-green-100 text-green-800' :
                                                appointment.status === 'confirmed' ? 'bg-blue-100 text-blue-800' :
                                                appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-gray-100 text-gray-800'
                                            }`}>
                                                {appointment.status}
                                            </span>
                                            <Button asChild size="sm" variant="outline">
                                                <Link href={`/client/appointments/${appointment.id}`}>
                                                    Voir
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                                <div className="text-center">
                                    <Button asChild variant="outline">
                                        <Link href="/client/appointments">
                                            Voir tous les rendez-vous
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun rendez-vous</h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    Commencez par prendre votre premier rendez-vous.
                                </p>
                                <div className="mt-6">
                                    <Button asChild>
                                        <Link href="/client/appointments/create">
                                            <Plus className="mr-2 h-4 w-4" />
                                            Prendre rendez-vous
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
