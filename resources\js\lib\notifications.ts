// Simple notification system for SmartCalendar
// In a real application, you might use a more sophisticated notification library

export interface Notification {
    id: string;
    title: string;
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
    timestamp: Date;
    read: boolean;
    eventId?: number;
}

class NotificationService {
    private notifications: Notification[] = [];
    private listeners: ((notifications: Notification[]) => void)[] = [];

    constructor() {
        // Load notifications from localStorage
        this.loadNotifications();
        
        // Check for upcoming events every minute
        setInterval(() => {
            this.checkUpcomingEvents();
        }, 60000); // 1 minute
    }

    private loadNotifications() {
        try {
            const stored = localStorage.getItem('smartcalendar_notifications');
            if (stored) {
                this.notifications = JSON.parse(stored).map((n: any) => ({
                    ...n,
                    timestamp: new Date(n.timestamp)
                }));
            }
        } catch (error) {
            console.error('Failed to load notifications:', error);
        }
    }

    private saveNotifications() {
        try {
            localStorage.setItem('smartcalendar_notifications', JSON.stringify(this.notifications));
        } catch (error) {
            console.error('Failed to save notifications:', error);
        }
    }

    private notifyListeners() {
        this.listeners.forEach(listener => listener([...this.notifications]));
    }

    private async checkUpcomingEvents() {
        try {
            const now = new Date();
            const in15Minutes = new Date(now.getTime() + 15 * 60 * 1000);
            const in1Hour = new Date(now.getTime() + 60 * 60 * 1000);

            const params = new URLSearchParams({
                start: now.toISOString().split('T')[0],
                end: in1Hour.toISOString().split('T')[0],
            });

            const response = await fetch(`/api/events?${params}`);
            const events = await response.json();

            events.forEach((event: any) => {
                const eventStart = new Date(event.start);
                const timeDiff = eventStart.getTime() - now.getTime();
                
                // Notify 15 minutes before
                if (timeDiff > 0 && timeDiff <= 15 * 60 * 1000) {
                    const existingNotification = this.notifications.find(
                        n => n.eventId === event.id && n.type === 'warning'
                    );
                    
                    if (!existingNotification) {
                        this.addNotification({
                            title: 'Événement dans 15 minutes',
                            message: `${event.title} commence bientôt`,
                            type: 'warning',
                            eventId: event.id
                        });
                    }
                }
                
                // Notify 1 hour before for high priority events
                if (event.extendedProps.priority === 'high' && 
                    timeDiff > 0 && timeDiff <= 60 * 60 * 1000) {
                    const existingNotification = this.notifications.find(
                        n => n.eventId === event.id && n.type === 'info'
                    );
                    
                    if (!existingNotification) {
                        this.addNotification({
                            title: 'Événement prioritaire dans 1 heure',
                            message: `${event.title} - Priorité élevée`,
                            type: 'info',
                            eventId: event.id
                        });
                    }
                }
            });
        } catch (error) {
            console.error('Failed to check upcoming events:', error);
        }
    }

    addNotification(notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) {
        const newNotification: Notification = {
            ...notification,
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
            read: false
        };

        this.notifications.unshift(newNotification);
        
        // Keep only the last 50 notifications
        if (this.notifications.length > 50) {
            this.notifications = this.notifications.slice(0, 50);
        }

        this.saveNotifications();
        this.notifyListeners();

        // Show browser notification if permission is granted
        this.showBrowserNotification(newNotification);
    }

    private showBrowserNotification(notification: Notification) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(notification.title, {
                body: notification.message,
                icon: '/favicon.ico',
                tag: notification.id
            });
        }
    }

    markAsRead(id: string) {
        const notification = this.notifications.find(n => n.id === id);
        if (notification) {
            notification.read = true;
            this.saveNotifications();
            this.notifyListeners();
        }
    }

    markAllAsRead() {
        this.notifications.forEach(n => n.read = true);
        this.saveNotifications();
        this.notifyListeners();
    }

    deleteNotification(id: string) {
        this.notifications = this.notifications.filter(n => n.id !== id);
        this.saveNotifications();
        this.notifyListeners();
    }

    clearAll() {
        this.notifications = [];
        this.saveNotifications();
        this.notifyListeners();
    }

    getNotifications(): Notification[] {
        return [...this.notifications];
    }

    getUnreadCount(): number {
        return this.notifications.filter(n => !n.read).length;
    }

    subscribe(listener: (notifications: Notification[]) => void) {
        this.listeners.push(listener);
        
        // Return unsubscribe function
        return () => {
            this.listeners = this.listeners.filter(l => l !== listener);
        };
    }

    async requestPermission(): Promise<boolean> {
        if (!('Notification' in window)) {
            return false;
        }

        if (Notification.permission === 'granted') {
            return true;
        }

        if (Notification.permission === 'denied') {
            return false;
        }

        const permission = await Notification.requestPermission();
        return permission === 'granted';
    }
}

// Create singleton instance
export const notificationService = new NotificationService();

// React hook for using notifications
export function useNotifications() {
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [unreadCount, setUnreadCount] = useState(0);

    useEffect(() => {
        const unsubscribe = notificationService.subscribe((notifications) => {
            setNotifications(notifications);
            setUnreadCount(notificationService.getUnreadCount());
        });

        // Initial load
        setNotifications(notificationService.getNotifications());
        setUnreadCount(notificationService.getUnreadCount());

        return unsubscribe;
    }, []);

    return {
        notifications,
        unreadCount,
        addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => 
            notificationService.addNotification(notification),
        markAsRead: (id: string) => notificationService.markAsRead(id),
        markAllAsRead: () => notificationService.markAllAsRead(),
        deleteNotification: (id: string) => notificationService.deleteNotification(id),
        clearAll: () => notificationService.clearAll(),
        requestPermission: () => notificationService.requestPermission(),
    };
}

// Import React hooks
import { useEffect, useState } from 'react';
