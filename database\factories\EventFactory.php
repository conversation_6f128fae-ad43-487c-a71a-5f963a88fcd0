<?php

namespace Database\Factories;

use App\Models\Calendar;
use App\Models\Category;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Event>
 */
class EventFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = fake()->dateTimeBetween('now', '+1 month');
        $endDate = (clone $startDate)->modify('+' . fake()->numberBetween(1, 4) . ' hours');

        return [
            'title' => fake()->sentence(3),
            'description' => fake()->paragraph(),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'all_day' => fake()->boolean(20), // 20% chance of being all day
            'location' => fake()->optional()->address(),
            'status' => fake()->randomElement(['confirmed', 'tentative', 'cancelled']),
            'priority' => fake()->randomElement(['low', 'medium', 'high']),
            'recurrence_type' => fake()->randomElement(['none', 'daily', 'weekly', 'monthly']),
            'recurrence_interval' => fake()->numberBetween(1, 3),
            'recurrence_days' => null,
            'recurrence_end_date' => fake()->optional()->dateTimeBetween('+1 month', '+6 months'),
            'recurrence_count' => fake()->optional()->numberBetween(5, 20),
            'user_id' => User::factory(),
            'calendar_id' => Calendar::factory(),
            'category_id' => fake()->optional()->randomElement([null, Category::factory()]),
            'metadata' => null,
        ];
    }

    /**
     * Indicate that the event is all day.
     */
    public function allDay(): static
    {
        return $this->state(fn (array $attributes) => [
            'all_day' => true,
            'start_date' => fake()->dateTimeBetween('now', '+1 month')->setTime(0, 0, 0),
            'end_date' => fake()->dateTimeBetween('now', '+1 month')->setTime(23, 59, 59),
        ]);
    }

    /**
     * Indicate that the event is high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'high',
        ]);
    }

    /**
     * Indicate that the event is recurring.
     */
    public function recurring(string $type = 'weekly'): static
    {
        return $this->state(fn (array $attributes) => [
            'recurrence_type' => $type,
            'recurrence_interval' => 1,
            'recurrence_end_date' => fake()->dateTimeBetween('+1 month', '+6 months'),
        ]);
    }

    /**
     * Indicate that the event is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
        ]);
    }

    /**
     * Indicate that the event is tentative.
     */
    public function tentative(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'tentative',
        ]);
    }
}
