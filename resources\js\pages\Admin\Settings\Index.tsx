import { Head, useForm } from '@inertiajs/react';
import { Settings, Building, Clock, DollarSign, Calendar } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import AppLayout from '@/layouts/app-layout';

interface BusinessInfo {
    name: string;
    address: string;
    phone: string;
    email: string;
    hours: {
        monday: string;
        tuesday: string;
        wednesday: string;
        thursday: string;
        friday: string;
        saturday: string;
        sunday: string;
    };
}

interface Service {
    name: string;
    price: number;
    duration: number;
    description: string;
}

interface AppointmentSettings {
    advance_booking_days: number;
    cancellation_hours: number;
    confirmation_required: boolean;
    auto_confirm_after_hours: number;
}

interface Settings {
    business_info: BusinessInfo;
    services: {
        basic: Service;
        premium: Service;
        deluxe: Service;
    };
    appointment_settings: AppointmentSettings;
}

interface SettingsIndexProps {
    settings: Settings;
}

export default function SettingsIndex({ settings }: SettingsIndexProps) {
    const { data, setData, put, processing, errors } = useForm({
        business_info: settings.business_info,
        services: settings.services,
        appointment_settings: settings.appointment_settings,
    });

    const submit = (e: React.FormEvent) => {
        e.preventDefault();
        put('/admin/settings');
    };

    const updateBusinessInfo = (field: string, value: string) => {
        setData('business_info', {
            ...data.business_info,
            [field]: value
        });
    };

    const updateService = (serviceType: 'basic' | 'premium' | 'deluxe', field: string, value: string | number) => {
        setData('services', {
            ...data.services,
            [serviceType]: {
                ...data.services[serviceType],
                [field]: value
            }
        });
    };

    const updateAppointmentSettings = (field: string, value: string | number | boolean) => {
        setData('appointment_settings', {
            ...data.appointment_settings,
            [field]: value
        });
    };

    return (
        <AppLayout>
            <Head title="Paramètres - AutoWash Admin" />
            
            <div className="space-y-6">
                {/* Header */}
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Paramètres</h1>
                    <p className="text-gray-600">Configurez les paramètres de votre centre de lavage</p>
                </div>

                <form onSubmit={submit} className="space-y-6">
                    {/* Informations de l'entreprise */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Building className="h-5 w-5" />
                                Informations de l'entreprise
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="business_name">Nom de l'entreprise</Label>
                                    <Input
                                        id="business_name"
                                        value={data.business_info.name}
                                        onChange={(e) => updateBusinessInfo('name', e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="business_phone">Téléphone</Label>
                                    <Input
                                        id="business_phone"
                                        value={data.business_info.phone}
                                        onChange={(e) => updateBusinessInfo('phone', e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="business_email">Email</Label>
                                    <Input
                                        id="business_email"
                                        type="email"
                                        value={data.business_info.email}
                                        onChange={(e) => updateBusinessInfo('email', e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="business_address">Adresse</Label>
                                    <Input
                                        id="business_address"
                                        value={data.business_info.address}
                                        onChange={(e) => updateBusinessInfo('address', e.target.value)}
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Horaires d'ouverture */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Clock className="h-5 w-5" />
                                Horaires d'ouverture
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {Object.entries(data.business_info.hours).map(([day, hours]) => {
                                    const dayLabels: { [key: string]: string } = {
                                        monday: 'Lundi',
                                        tuesday: 'Mardi',
                                        wednesday: 'Mercredi',
                                        thursday: 'Jeudi',
                                        friday: 'Vendredi',
                                        saturday: 'Samedi',
                                        sunday: 'Dimanche'
                                    };

                                    return (
                                        <div key={day} className="space-y-2">
                                            <Label>{dayLabels[day]}</Label>
                                            <Input
                                                value={hours}
                                                onChange={(e) => updateBusinessInfo('hours', {
                                                    ...data.business_info.hours,
                                                    [day]: e.target.value
                                                })}
                                                placeholder="08:00-18:00 ou Fermé"
                                            />
                                        </div>
                                    );
                                })}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Services et tarifs */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <DollarSign className="h-5 w-5" />
                                Services et tarifs
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {Object.entries(data.services).map(([serviceType, service]) => {
                                const serviceLabels: { [key: string]: string } = {
                                    basic: 'Lavage Basique',
                                    premium: 'Lavage Premium',
                                    deluxe: 'Lavage Deluxe'
                                };

                                return (
                                    <div key={serviceType} className="border rounded-lg p-4">
                                        <h4 className="font-medium mb-4">{serviceLabels[serviceType]}</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div className="space-y-2">
                                                <Label>Prix (DH)</Label>
                                                <Input
                                                    type="number"
                                                    value={service.price}
                                                    onChange={(e) => updateService(
                                                        serviceType as 'basic' | 'premium' | 'deluxe',
                                                        'price',
                                                        parseInt(e.target.value)
                                                    )}
                                                />
                                            </div>
                                            <div className="space-y-2">
                                                <Label>Durée (minutes)</Label>
                                                <Input
                                                    type="number"
                                                    value={service.duration}
                                                    onChange={(e) => updateService(
                                                        serviceType as 'basic' | 'premium' | 'deluxe',
                                                        'duration',
                                                        parseInt(e.target.value)
                                                    )}
                                                />
                                            </div>
                                            <div className="space-y-2">
                                                <Label>Description</Label>
                                                <Input
                                                    value={service.description}
                                                    onChange={(e) => updateService(
                                                        serviceType as 'basic' | 'premium' | 'deluxe',
                                                        'description',
                                                        e.target.value
                                                    )}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </CardContent>
                    </Card>

                    {/* Paramètres des rendez-vous */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Paramètres des rendez-vous
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label>Réservation à l'avance (jours)</Label>
                                    <Input
                                        type="number"
                                        value={data.appointment_settings.advance_booking_days}
                                        onChange={(e) => updateAppointmentSettings('advance_booking_days', parseInt(e.target.value))}
                                    />
                                    <p className="text-xs text-gray-500">
                                        Nombre maximum de jours à l'avance pour réserver
                                    </p>
                                </div>
                                <div className="space-y-2">
                                    <Label>Délai d'annulation (heures)</Label>
                                    <Input
                                        type="number"
                                        value={data.appointment_settings.cancellation_hours}
                                        onChange={(e) => updateAppointmentSettings('cancellation_hours', parseInt(e.target.value))}
                                    />
                                    <p className="text-xs text-gray-500">
                                        Délai minimum avant le RDV pour annuler
                                    </p>
                                </div>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label>Confirmation requise</Label>
                                    <p className="text-xs text-gray-500">
                                        Les rendez-vous doivent être confirmés par un admin
                                    </p>
                                </div>
                                <Switch
                                    checked={data.appointment_settings.confirmation_required}
                                    onCheckedChange={(checked) => updateAppointmentSettings('confirmation_required', checked)}
                                />
                            </div>
                            
                            {data.appointment_settings.confirmation_required && (
                                <div className="space-y-2">
                                    <Label>Auto-confirmation après (heures)</Label>
                                    <Input
                                        type="number"
                                        value={data.appointment_settings.auto_confirm_after_hours}
                                        onChange={(e) => updateAppointmentSettings('auto_confirm_after_hours', parseInt(e.target.value))}
                                    />
                                    <p className="text-xs text-gray-500">
                                        Confirmer automatiquement après X heures si pas de réponse
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Bouton de sauvegarde */}
                    <div className="flex justify-end">
                        <Button type="submit" disabled={processing} className="min-w-32">
                            {processing ? 'Sauvegarde...' : 'Sauvegarder'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
