<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'start_date',
        'end_date',
        'all_day',
        'location',
        'status',
        'priority',
        'recurrence_type',
        'recurrence_interval',
        'recurrence_days',
        'recurrence_end_date',
        'recurrence_count',
        'user_id',
        'calendar_id',
        'category_id',
        'metadata',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'all_day' => 'boolean',
        'recurrence_days' => 'array',
        'recurrence_end_date' => 'date',
        'recurrence_interval' => 'integer',
        'recurrence_count' => 'integer',
        'user_id' => 'integer',
        'calendar_id' => 'integer',
        'category_id' => 'integer',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the event.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the calendar that contains the event.
     */
    public function calendar(): BelongsTo
    {
        return $this->belongsTo(Calendar::class);
    }

    /**
     * Get the category of the event.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Scope a query to only include events for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include events within a date range.
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->where(function ($query) use ($startDate, $endDate) {
            $query->whereBetween('start_date', [$startDate, $endDate])
                  ->orWhereBetween('end_date', [$startDate, $endDate])
                  ->orWhere(function ($query) use ($startDate, $endDate) {
                      $query->where('start_date', '<=', $startDate)
                            ->where('end_date', '>=', $endDate);
                  });
        });
    }

    /**
     * Scope a query to only include events for today.
     */
    public function scopeToday($query)
    {
        $today = Carbon::today();
        return $query->betweenDates($today->startOfDay(), $today->endOfDay());
    }

    /**
     * Scope a query to only include upcoming events.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>=', Carbon::now());
    }

    /**
     * Check if the event is recurring.
     */
    public function isRecurring(): bool
    {
        return $this->recurrence_type !== 'none';
    }

    /**
     * Check if the event is all day.
     */
    public function isAllDay(): bool
    {
        return $this->all_day;
    }

    /**
     * Get the duration of the event in minutes.
     */
    public function getDurationInMinutes(): int
    {
        return $this->start_date->diffInMinutes($this->end_date);
    }
}
