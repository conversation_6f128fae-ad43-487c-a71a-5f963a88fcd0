@echo off
echo Setting up SmartCalendar database...
echo.

echo Running database migrations...
call php artisan migrate:fresh --force
if %errorlevel% neq 0 (
    echo Error running migrations
    pause
    exit /b 1
)

echo.
echo Seeding database with sample data...
call php artisan db:seed --force
if %errorlevel% neq 0 (
    echo Error seeding database
    pause
    exit /b 1
)

echo.
echo Database setup completed successfully!
echo.
pause
