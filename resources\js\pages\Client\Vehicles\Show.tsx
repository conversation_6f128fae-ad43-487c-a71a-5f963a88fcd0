import { Head, <PERSON>, router } from '@inertiajs/react';
import { ArrowLeft, Car, Calendar, Edit, Trash2, Plus, AlertTriangle, Clock } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';

interface Vehicle {
    id: number;
    brand: string;
    model: string;
    year: number;
    color: string;
    license_plate: string;
    vehicle_type: string;
    notes?: string;
    created_at: string;
    updated_at: string;
}

interface Appointment {
    id: number;
    scheduled_at: string;
    service_type: string;
    status: string;
    price: number;
}

interface ShowVehicleProps {
    vehicle: Vehicle;
    appointments: Appointment[];
    stats: {
        total_appointments: number;
        completed_appointments: number;
        pending_appointments: number;
        total_spent: number;
        last_appointment?: string;
    };
}

export default function ShowVehicle({ vehicle, appointments, stats }: ShowVehicleProps) {
    const handleDeleteRequest = () => {
        const hasAppointments = stats.total_appointments > 0;
        
        if (hasAppointments) {
            if (confirm(`Ce véhicule a ${stats.total_appointments} rendez-vous associé(s). Êtes-vous sûr de vouloir demander sa suppression ?`)) {
                router.post(`/client/vehicles/${vehicle.id}/request-deletion`, {}, {
                    onSuccess: () => {
                        // Redirection gérée par le contrôleur
                    }
                });
            }
        } else {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce véhicule ?')) {
                router.delete(`/client/vehicles/${vehicle.id}`, {
                    onSuccess: () => {
                        // Redirection gérée par le contrôleur
                    }
                });
            }
        }
    };

    const getVehicleTypeLabel = (type: string) => {
        switch (type) {
            case 'car': return 'Voiture';
            case 'suv': return 'SUV';
            case 'truck': return 'Camion';
            case 'motorcycle': return 'Moto';
            case 'van': return 'Fourgon';
            default: return type;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800';
            case 'in_progress':
                return 'bg-yellow-100 text-yellow-800';
            case 'pending':
                return 'bg-orange-100 text-orange-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed': return 'Terminé';
            case 'confirmed': return 'Confirmé';
            case 'in_progress': return 'En cours';
            case 'pending': return 'En attente';
            case 'cancelled': return 'Annulé';
            default: return status;
        }
    };

    return (
        <AppLayout>
            <Head title={`${vehicle.brand} ${vehicle.model} - AutoWash`} />
            
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button asChild variant="outline" size="sm">
                        <Link href="/client/vehicles">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Retour
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">
                            {vehicle.brand} {vehicle.model}
                        </h1>
                        <p className="text-gray-600">Détails et historique de votre véhicule</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Informations du véhicule */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Car className="h-5 w-5" />
                                Informations du véhicule
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Type</span>
                                <Badge variant="secondary">
                                    {getVehicleTypeLabel(vehicle.vehicle_type)}
                                </Badge>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Marque et modèle</span>
                                <span className="text-sm font-medium">
                                    {vehicle.brand} {vehicle.model}
                                </span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Année</span>
                                <span className="text-sm">{vehicle.year}</span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Couleur</span>
                                <span className="text-sm">{vehicle.color}</span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Plaque</span>
                                <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                    {vehicle.license_plate}
                                </span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Ajouté le</span>
                                <span className="text-sm text-gray-600">
                                    {new Date(vehicle.created_at).toLocaleDateString('fr-FR')}
                                </span>
                            </div>

                            {vehicle.notes && (
                                <div className="border-t pt-4">
                                    <span className="text-sm font-medium text-gray-600">Notes</span>
                                    <p className="text-sm text-gray-700 mt-1">{vehicle.notes}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Statistiques */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Statistiques
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Total rendez-vous</span>
                                <span className="text-lg font-bold text-blue-600">
                                    {stats.total_appointments}
                                </span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Lavages terminés</span>
                                <span className="text-lg font-bold text-green-600">
                                    {stats.completed_appointments}
                                </span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">En attente</span>
                                <span className="text-lg font-bold text-orange-600">
                                    {stats.pending_appointments}
                                </span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Total dépensé</span>
                                <span className="text-lg font-bold text-green-600">
                                    {stats.total_spent} DH
                                </span>
                            </div>
                            
                            {stats.last_appointment && (
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Dernier lavage</span>
                                    <span className="text-sm text-gray-600">
                                        {new Date(stats.last_appointment).toLocaleDateString('fr-FR')}
                                    </span>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Historique des rendez-vous */}
                <Card>
                    <CardHeader>
                        <CardTitle>Historique des rendez-vous</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {appointments.length > 0 ? (
                            <div className="space-y-3">
                                {appointments.map((appointment) => (
                                    <div key={appointment.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <Clock className="h-4 w-4 text-gray-400" />
                                            <div>
                                                <p className="text-sm font-medium">
                                                    {appointment.service_type} - {appointment.price} DH
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {new Date(appointment.scheduled_at).toLocaleDateString('fr-FR')} à{' '}
                                                    {new Date(appointment.scheduled_at).toLocaleTimeString('fr-FR', { 
                                                        hour: '2-digit', 
                                                        minute: '2-digit' 
                                                    })}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Badge className={getStatusColor(appointment.status)}>
                                                {getStatusText(appointment.status)}
                                            </Badge>
                                            <Button asChild size="sm" variant="outline">
                                                <Link href={`/client/appointments/${appointment.id}`}>
                                                    Voir
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun rendez-vous</h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    Ce véhicule n'a pas encore de rendez-vous programmé.
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Actions */}
                <Card>
                    <CardContent className="pt-6">
                        <div className="flex flex-wrap gap-4">
                            <Button asChild>
                                <Link href={`/client/appointments/create?vehicle_id=${vehicle.id}`}>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Prendre rendez-vous
                                </Link>
                            </Button>
                            <Button asChild variant="outline">
                                <Link href={`/client/vehicles/${vehicle.id}/edit`}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Modifier
                                </Link>
                            </Button>
                            <Button 
                                variant="outline" 
                                className="text-red-600 hover:text-red-700 hover:border-red-300"
                                onClick={handleDeleteRequest}
                            >
                                {stats.total_appointments > 0 ? (
                                    <>
                                        <AlertTriangle className="mr-2 h-4 w-4" />
                                        Demander suppression
                                    </>
                                ) : (
                                    <>
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Supprimer
                                    </>
                                )}
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
