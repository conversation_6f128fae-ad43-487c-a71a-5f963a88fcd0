import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { ArrowLeft, User, Car, Calendar, Phone, Mail, Clock } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
    created_at: string;
    vehicles: Array<{
        id: number;
        brand: string;
        model: string;
        license_plate: string;
        vehicle_type: string;
    }>;
    appointments: Array<{
        id: number;
        scheduled_at: string;
        service_type: string;
        status: string;
        price: number;
        vehicle: {
            brand: string;
            model: string;
            license_plate: string;
        };
    }>;
}

interface Stats {
    total_vehicles: number;
    total_appointments: number;
    completed_appointments: number;
    pending_appointments: number;
    total_spent: number;
    last_appointment?: string;
}

interface ShowUserProps {
    user: User;
    stats: Stats;
}

export default function ShowUser({ user, stats }: ShowUserProps) {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed': return 'bg-green-100 text-green-800';
            case 'confirmed': return 'bg-blue-100 text-blue-800';
            case 'in_progress': return 'bg-yellow-100 text-yellow-800';
            case 'pending': return 'bg-orange-100 text-orange-800';
            case 'cancelled': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed': return 'Terminé';
            case 'confirmed': return 'Confirmé';
            case 'in_progress': return 'En cours';
            case 'pending': return 'En attente';
            case 'cancelled': return 'Annulé';
            default: return status;
        }
    };

    const getServiceLabel = (serviceType: string) => {
        switch (serviceType) {
            case 'basic': return 'Lavage Basique';
            case 'premium': return 'Lavage Premium';
            case 'deluxe': return 'Lavage Deluxe';
            default: return serviceType;
        }
    };

    return (
        <AppLayout>
            <Head title={`${user.name} - Profil Client`} />
            
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button asChild variant="outline" size="sm">
                        <Link href="/admin/users">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Retour
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">{user.name}</h1>
                        <p className="text-gray-600">Profil client détaillé</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Informations du client */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Informations personnelles
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Nom</span>
                                <span className="text-sm font-medium">{user.name}</span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Email</span>
                                <span className="text-sm">{user.email}</span>
                            </div>
                            
                            {user.phone && (
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Téléphone</span>
                                    <span className="text-sm">{user.phone}</span>
                                </div>
                            )}
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Client depuis</span>
                                <span className="text-sm">
                                    {new Date(user.created_at).toLocaleDateString('fr-FR')}
                                </span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Statistiques */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Statistiques
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Véhicules</span>
                                <span className="text-lg font-bold text-blue-600">{stats.total_vehicles}</span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Total RDV</span>
                                <span className="text-lg font-bold text-green-600">{stats.total_appointments}</span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">RDV terminés</span>
                                <span className="text-lg font-bold text-green-600">{stats.completed_appointments}</span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">En attente</span>
                                <span className="text-lg font-bold text-orange-600">{stats.pending_appointments}</span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Total dépensé</span>
                                <span className="text-lg font-bold text-green-600">{stats.total_spent} DH</span>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Véhicules */}
                <Card>
                    <CardHeader>
                        <CardTitle>Véhicules ({user.vehicles.length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {user.vehicles.length > 0 ? (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {user.vehicles.map((vehicle) => (
                                    <div key={vehicle.id} className="p-4 border rounded-lg">
                                        <div className="flex items-center gap-2 mb-2">
                                            <Car className="w-4 h-4 text-gray-400" />
                                            <span className="font-medium">
                                                {vehicle.brand} {vehicle.model}
                                            </span>
                                        </div>
                                        <p className="text-sm text-gray-600">
                                            Plaque: {vehicle.license_plate}
                                        </p>
                                        <p className="text-sm text-gray-600 capitalize">
                                            Type: {vehicle.vehicle_type}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <p className="text-gray-500 text-center py-4">Aucun véhicule enregistré</p>
                        )}
                    </CardContent>
                </Card>

                {/* Historique des rendez-vous */}
                <Card>
                    <CardHeader>
                        <CardTitle>Historique des rendez-vous ({user.appointments.length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {user.appointments.length > 0 ? (
                            <div className="space-y-3">
                                {user.appointments.slice(0, 10).map((appointment) => (
                                    <div key={appointment.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <Clock className="h-4 w-4 text-gray-400" />
                                            <div>
                                                <p className="text-sm font-medium">
                                                    {getServiceLabel(appointment.service_type)} - {appointment.price} DH
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {appointment.vehicle.brand} {appointment.vehicle.model} ({appointment.vehicle.license_plate})
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {new Date(appointment.scheduled_at).toLocaleDateString('fr-FR')} à{' '}
                                                    {new Date(appointment.scheduled_at).toLocaleTimeString('fr-FR', { 
                                                        hour: '2-digit', 
                                                        minute: '2-digit' 
                                                    })}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Badge className={getStatusColor(appointment.status)}>
                                                {getStatusText(appointment.status)}
                                            </Badge>
                                            <Button asChild size="sm" variant="outline">
                                                <Link href={`/admin/appointments/${appointment.id}`}>
                                                    Voir
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                                {user.appointments.length > 10 && (
                                    <p className="text-center text-sm text-gray-500 pt-2">
                                        ... et {user.appointments.length - 10} autre{user.appointments.length - 10 > 1 ? 's' : ''} rendez-vous
                                    </p>
                                )}
                            </div>
                        ) : (
                            <p className="text-gray-500 text-center py-4">Aucun rendez-vous</p>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
