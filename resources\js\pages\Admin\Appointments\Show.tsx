import { Head, <PERSON>, router } from '@inertiajs/react';
import { ArrowLeft, Calendar, Car, Clock, User, Phone, Mail, MapPin, CreditCard, FileText, CheckCircle, XCircle, AlertCircle, Receipt } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';

interface Appointment {
    id: number;
    scheduled_at: string;
    service_type: string;
    status: string;
    price: number;
    duration: number;
    notes?: string;
    admin_notes?: string;
    created_at: string;
    invoice?: {
        id: number;
        invoice_number: string;
        status: string;
        total_amount: number;
    };
    user: {
        id: number;
        name: string;
        email: string;
        phone?: string;
    };
    vehicle: {
        id: number;
        brand: string;
        model: string;
        year: number;
        color: string;
        license_plate: string;
        vehicle_type: string;
    };
}

interface ShowAppointmentProps {
    appointment: Appointment;
}

export default function ShowAppointment({ appointment }: ShowAppointmentProps) {
    const handleStatusUpdate = (newStatus: string) => {
        router.patch(`/admin/appointments/${appointment.id}/status`, {
            status: newStatus
        });
    };

    const handleGenerateInvoice = () => {
        router.post(`/admin/appointments/${appointment.id}/generate-invoice`);
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800';
            case 'in_progress':
                return 'bg-yellow-100 text-yellow-800';
            case 'pending':
                return 'bg-orange-100 text-orange-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed': return 'Terminé';
            case 'confirmed': return 'Confirmé';
            case 'in_progress': return 'En cours';
            case 'pending': return 'En attente';
            case 'cancelled': return 'Annulé';
            default: return status;
        }
    };

    const getServiceLabel = (serviceType: string) => {
        switch (serviceType) {
            case 'basic': return 'Lavage Basique';
            case 'premium': return 'Lavage Premium';
            case 'deluxe': return 'Lavage Deluxe';
            default: return serviceType;
        }
    };

    const getVehicleTypeLabel = (type: string) => {
        switch (type) {
            case 'car': return 'Voiture';
            case 'suv': return 'SUV';
            case 'truck': return 'Camion';
            case 'motorcycle': return 'Moto';
            case 'van': return 'Fourgon';
            default: return type;
        }
    };

    return (
        <AppLayout>
            <Head title={`Rendez-vous #${appointment.id} - AutoWash Admin`} />

            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button asChild variant="outline" size="sm">
                        <Link href="/admin/appointments">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Retour
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Rendez-vous #{appointment.id}</h1>
                        <p className="text-gray-600">Détails complets du rendez-vous</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Informations du client */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Informations du client
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Nom</span>
                                <span className="text-sm font-medium">{appointment.user.name}</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Email</span>
                                <span className="text-sm">{appointment.user.email}</span>
                            </div>

                            {appointment.user.phone && (
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Téléphone</span>
                                    <span className="text-sm">{appointment.user.phone}</span>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Informations du véhicule */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Car className="h-5 w-5" />
                                Véhicule
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Véhicule</span>
                                <span className="text-sm font-medium">
                                    {appointment.vehicle.brand} {appointment.vehicle.model}
                                </span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Année</span>
                                <span className="text-sm">{appointment.vehicle.year}</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Couleur</span>
                                <span className="text-sm">{appointment.vehicle.color}</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Plaque</span>
                                <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                    {appointment.vehicle.license_plate}
                                </span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Type</span>
                                <span className="text-sm">{getVehicleTypeLabel(appointment.vehicle.vehicle_type)}</span>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Détails du rendez-vous */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Calendar className="h-5 w-5" />
                            Détails du rendez-vous
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Statut</span>
                                <Badge className={getStatusColor(appointment.status)}>
                                    {getStatusText(appointment.status)}
                                </Badge>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Service</span>
                                <span className="text-sm font-medium">{getServiceLabel(appointment.service_type)}</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Prix</span>
                                <span className="text-lg font-bold text-green-600">{appointment.price} DH</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Durée</span>
                                <span className="text-sm">{appointment.duration} minutes</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Date et heure</span>
                                <div className="text-right">
                                    <div className="text-sm font-medium">
                                        {new Date(appointment.scheduled_at).toLocaleDateString('fr-FR')}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        {new Date(appointment.scheduled_at).toLocaleTimeString('fr-FR', {
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        })}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Créé le</span>
                                <span className="text-sm text-gray-600">
                                    {new Date(appointment.created_at).toLocaleDateString('fr-FR')}
                                </span>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Notes */}
                {(appointment.notes || appointment.admin_notes) && (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {appointment.notes && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <FileText className="h-5 w-5" />
                                        Notes du client
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-sm text-gray-700">{appointment.notes}</p>
                                </CardContent>
                            </Card>
                        )}

                        {appointment.admin_notes && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        Notes administrateur
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-sm text-gray-700">{appointment.admin_notes}</p>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                )}

                {/* Actions */}
                <Card>
                    <CardContent className="pt-6">
                        <div className="space-y-4">
                            {/* Actions de workflow */}
                            <div className="flex flex-wrap gap-4">
                                {appointment.status === 'pending' && (
                                    <Button onClick={() => handleStatusUpdate('confirmed')}>
                                        <CheckCircle className="mr-2 h-4 w-4" />
                                        Confirmer le rendez-vous
                                    </Button>
                                )}

                                {appointment.status === 'confirmed' && (
                                    <Button
                                        onClick={() => handleStatusUpdate('in_progress')}
                                        className="bg-yellow-600 hover:bg-yellow-700"
                                    >
                                        <AlertCircle className="mr-2 h-4 w-4" />
                                        Démarrer le lavage
                                    </Button>
                                )}

                                {appointment.status === 'in_progress' && (
                                    <Button
                                        onClick={() => handleStatusUpdate('completed')}
                                        className="bg-green-600 hover:bg-green-700"
                                    >
                                        <CheckCircle className="mr-2 h-4 w-4" />
                                        Marquer comme terminé
                                    </Button>
                                )}

                                {['pending', 'confirmed'].includes(appointment.status) && (
                                    <Button
                                        variant="outline"
                                        onClick={() => handleStatusUpdate('cancelled')}
                                        className="text-red-600 hover:text-red-700"
                                    >
                                        <XCircle className="mr-2 h-4 w-4" />
                                        Annuler le rendez-vous
                                    </Button>
                                )}
                            </div>

                            {/* Actions de facturation */}
                            {appointment.status === 'completed' && (
                                <div className="border-t pt-4">
                                    <h4 className="font-medium mb-3">Facturation</h4>
                                    {appointment.invoice ? (
                                        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                            <div>
                                                <p className="text-sm font-medium text-green-800">
                                                    Facture générée: {appointment.invoice.invoice_number}
                                                </p>
                                                <p className="text-xs text-green-600">
                                                    Montant: {appointment.invoice.total_amount} DH - Statut: {appointment.invoice.status}
                                                </p>
                                            </div>
                                            <Button asChild size="sm" variant="outline">
                                                <Link href={`/admin/invoices/${appointment.invoice.id}`}>
                                                    <Receipt className="mr-2 h-4 w-4" />
                                                    Voir la facture
                                                </Link>
                                            </Button>
                                        </div>
                                    ) : (
                                        <Button onClick={handleGenerateInvoice} variant="outline">
                                            <Receipt className="mr-2 h-4 w-4" />
                                            Générer la facture
                                        </Button>
                                    )}
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
