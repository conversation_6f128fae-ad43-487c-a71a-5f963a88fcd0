import { Head, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { Receipt, Search, Eye, Filter, Download, DollarSign, Calendar, User } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';

interface Invoice {
    id: number;
    invoice_number: string;
    subtotal: number;
    tax_amount: number;
    total_amount: number;
    status: string;
    due_date: string;
    paid_at?: string;
    created_at: string;
    appointment: {
        id: number;
        service_type: string;
        scheduled_at: string;
        user: {
            id: number;
            name: string;
            email: string;
        };
        vehicle: {
            brand: string;
            model: string;
            license_plate: string;
        };
    };
}

interface Stats {
    total_invoices: number;
    paid_invoices: number;
    pending_invoices: number;
    overdue_invoices: number;
    total_amount: number;
    pending_amount: number;
}

interface InvoicesIndexProps {
    invoices: {
        data: Invoice[];
        links?: any[];
        meta?: any;
    };
    stats: Stats;
    filters: {
        status?: string;
        search?: string;
        date_from?: string;
        date_to?: string;
    };
}

export default function InvoicesIndex({ invoices, stats, filters }: InvoicesIndexProps) {
    const [search, setSearch] = useState(filters.search || '');
    const [statusFilter, setStatusFilter] = useState(filters.status || 'all');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/admin/invoices', { 
            search, 
            status: statusFilter === 'all' ? '' : statusFilter 
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const handleStatusFilter = (status: string) => {
        setStatusFilter(status);
        router.get('/admin/invoices', { 
            search, 
            status: status === 'all' ? '' : status 
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const handleStatusUpdate = (invoiceId: number, newStatus: string) => {
        router.patch(`/admin/invoices/${invoiceId}/status`, {
            status: newStatus
        }, {
            preserveScroll: true,
        });
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'paid': return 'bg-green-100 text-green-800';
            case 'pending': return 'bg-orange-100 text-orange-800';
            case 'overdue': return 'bg-red-100 text-red-800';
            case 'cancelled': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'paid': return 'Payée';
            case 'pending': return 'En attente';
            case 'overdue': return 'En retard';
            case 'cancelled': return 'Annulée';
            default: return status;
        }
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('fr-FR').format(amount) + ' DH';
    };

    return (
        <AppLayout>
            <Head title="Gestion de la Facturation - AutoWash Admin" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Gestion de la Facturation</h1>
                        <p className="text-gray-600">Gérez toutes les factures ({invoices?.meta?.total || invoices?.data?.length || 0} factures)</p>
                    </div>
                </div>

                {/* Statistiques */}
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    <Card>
                        <CardContent className="p-4">
                            <div className="text-2xl font-bold text-blue-600">{stats.total_invoices}</div>
                            <p className="text-xs text-gray-600">Total factures</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-4">
                            <div className="text-2xl font-bold text-green-600">{stats.paid_invoices}</div>
                            <p className="text-xs text-gray-600">Payées</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-4">
                            <div className="text-2xl font-bold text-orange-600">{stats.pending_invoices}</div>
                            <p className="text-xs text-gray-600">En attente</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-4">
                            <div className="text-2xl font-bold text-red-600">{stats.overdue_invoices}</div>
                            <p className="text-xs text-gray-600">En retard</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-4">
                            <div className="text-lg font-bold text-green-600">{formatCurrency(stats.total_amount)}</div>
                            <p className="text-xs text-gray-600">CA encaissé</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-4">
                            <div className="text-lg font-bold text-orange-600">{formatCurrency(stats.pending_amount)}</div>
                            <p className="text-xs text-gray-600">En attente</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Filtres et recherche */}
                <Card>
                    <CardContent className="p-4">
                        <div className="flex flex-col lg:flex-row gap-4">
                            <form onSubmit={handleSearch} className="flex gap-4 flex-1">
                                <div className="flex-1">
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                        <Input
                                            type="text"
                                            placeholder="Rechercher par numéro de facture ou client..."
                                            value={search}
                                            onChange={(e) => setSearch(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                <Button type="submit">Rechercher</Button>
                            </form>
                            
                            <div className="flex items-center gap-2">
                                <Filter className="w-4 h-4 text-gray-500" />
                                <Select value={statusFilter} onValueChange={handleStatusFilter}>
                                    <SelectTrigger className="w-40">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Tous statuts</SelectItem>
                                        <SelectItem value="pending">En attente</SelectItem>
                                        <SelectItem value="paid">Payées</SelectItem>
                                        <SelectItem value="overdue">En retard</SelectItem>
                                        <SelectItem value="cancelled">Annulées</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            {(filters.search || filters.status) && (
                                <Button 
                                    variant="outline"
                                    onClick={() => {
                                        setSearch('');
                                        setStatusFilter('all');
                                        router.get('/admin/invoices');
                                    }}
                                >
                                    Effacer
                                </Button>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Liste des factures */}
                {invoices?.data && invoices.data.length > 0 ? (
                    <div className="space-y-4">
                        {invoices.data.map((invoice) => (
                            <Card key={invoice.id} className="hover:shadow-md transition-shadow">
                                <CardContent className="p-6">
                                    <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
                                        {/* Informations de la facture */}
                                        <div>
                                            <div className="flex items-center gap-2 mb-2">
                                                <Receipt className="w-4 h-4 text-gray-400" />
                                                <span className="font-mono text-sm font-medium">
                                                    {invoice.invoice_number}
                                                </span>
                                            </div>
                                            <p className="text-sm text-gray-600">
                                                Créée le {new Date(invoice.created_at).toLocaleDateString('fr-FR')}
                                            </p>
                                            <p className="text-sm text-gray-600">
                                                Échéance: {new Date(invoice.due_date).toLocaleDateString('fr-FR')}
                                            </p>
                                        </div>

                                        {/* Client et véhicule */}
                                        <div>
                                            <div className="flex items-center gap-2 mb-2">
                                                <User className="w-4 h-4 text-gray-400" />
                                                <span className="text-sm font-medium">
                                                    {invoice.appointment.user.name}
                                                </span>
                                            </div>
                                            <p className="text-xs text-gray-600">
                                                {invoice.appointment.vehicle.brand} {invoice.appointment.vehicle.model}
                                            </p>
                                            <p className="text-xs text-gray-600">
                                                {invoice.appointment.vehicle.license_plate}
                                            </p>
                                        </div>

                                        {/* Montants */}
                                        <div>
                                            <div className="text-lg font-bold text-green-600">
                                                {formatCurrency(invoice.total_amount)}
                                            </div>
                                            <p className="text-xs text-gray-600">
                                                HT: {formatCurrency(invoice.subtotal)}
                                            </p>
                                            <p className="text-xs text-gray-600">
                                                TVA: {formatCurrency(invoice.tax_amount)}
                                            </p>
                                        </div>

                                        {/* Actions et statut */}
                                        <div className="flex flex-col gap-3">
                                            <Badge className={getStatusColor(invoice.status)}>
                                                {getStatusText(invoice.status)}
                                            </Badge>
                                            
                                            <div className="flex flex-col gap-2">
                                                <Button asChild size="sm" variant="outline" className="w-full">
                                                    <Link href={`/admin/invoices/${invoice.id}`}>
                                                        <Eye className="w-4 h-4 mr-1" />
                                                        Voir
                                                    </Link>
                                                </Button>
                                                
                                                {invoice.status === 'pending' && (
                                                    <Button 
                                                        size="sm" 
                                                        onClick={() => handleStatusUpdate(invoice.id, 'paid')}
                                                        className="w-full bg-green-600 hover:bg-green-700"
                                                    >
                                                        <DollarSign className="w-4 h-4 mr-1" />
                                                        Marquer payée
                                                    </Button>
                                                )}
                                                
                                                <Button asChild size="sm" variant="outline" className="w-full">
                                                    <Link href={`/admin/invoices/${invoice.id}/download`}>
                                                        <Download className="w-4 h-4 mr-1" />
                                                        PDF
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Receipt className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune facture trouvée</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                {filters.search || filters.status 
                                    ? 'Aucune facture ne correspond à vos critères.'
                                    : 'Aucune facture générée pour le moment.'
                                }
                            </p>
                        </CardContent>
                    </Card>
                )}

                {/* Pagination */}
                {invoices?.links && invoices.links.length > 3 && (
                    <div className="flex justify-center">
                        <div className="flex gap-2">
                            {invoices.links.map((link, index) => (
                                <Button
                                    key={index}
                                    variant={link.active ? "default" : "outline"}
                                    size="sm"
                                    disabled={!link.url}
                                    onClick={() => link.url && router.get(link.url)}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
