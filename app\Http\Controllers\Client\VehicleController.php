<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class VehicleController extends Controller
{
    use AuthorizesRequests;
    /**
     * Display a listing of the user's vehicles.
     */
    public function index()
    {
        $vehicles = Vehicle::where('user_id', Auth::id())->latest()->get();

        return Inertia::render('Client/Vehicles/Index', [
            'vehicles' => $vehicles,
        ]);
    }

    /**
     * Show the form for creating a new vehicle.
     */
    public function create()
    {
        return Inertia::render('Client/Vehicles/Create');
    }

    /**
     * Store a newly created vehicle in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'brand' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'color' => 'required|string|max:255',
            'license_plate' => 'required|string|max:255|unique:vehicles',
            'vehicle_type' => 'required|in:car,suv,truck,van,motorcycle,other',
            'notes' => 'nullable|string|max:1000',
        ]);

        $validated['user_id'] = Auth::id();

        Vehicle::create($validated);

        return redirect()->route('client.vehicles.index')
            ->with('success', 'Véhicule ajouté avec succès !');
    }

    /**
     * Display the specified vehicle.
     */
    public function show(Vehicle $vehicle)
    {
        $this->authorize('view', $vehicle);

        $vehicle->load('appointments');

        return Inertia::render('Client/Vehicles/Show', [
            'vehicle' => $vehicle,
        ]);
    }

    /**
     * Show the form for editing the specified vehicle.
     */
    public function edit(Vehicle $vehicle)
    {
        $this->authorize('update', $vehicle);

        return Inertia::render('Client/Vehicles/Edit', [
            'vehicle' => $vehicle,
        ]);
    }

    /**
     * Update the specified vehicle in storage.
     */
    public function update(Request $request, Vehicle $vehicle)
    {
        $this->authorize('update', $vehicle);

        $validated = $request->validate([
            'brand' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'color' => 'required|string|max:255',
            'license_plate' => 'required|string|max:255|unique:vehicles,license_plate,' . $vehicle->id,
            'vehicle_type' => 'required|in:car,suv,truck,van,motorcycle,other',
            'notes' => 'nullable|string|max:1000',
        ]);

        $vehicle->update($validated);

        return redirect()->route('client.vehicles.index')
            ->with('success', 'Véhicule modifié avec succès !');
    }

    /**
     * Remove the specified vehicle from storage.
     */
    public function destroy(Vehicle $vehicle)
    {
        $this->authorize('delete', $vehicle);

        $vehicle->delete();

        return redirect()->route('client.vehicles.index')
            ->with('success', 'Véhicule supprimé avec succès !');
    }
}
