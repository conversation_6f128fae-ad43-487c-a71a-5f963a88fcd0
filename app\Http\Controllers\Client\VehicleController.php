<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class VehicleController extends Controller
{
    use AuthorizesRequests;
    /**
     * Display a listing of the user's vehicles.
     */
    public function index()
    {
        $vehicles = Vehicle::where('user_id', Auth::id())
            ->withCount('appointments')
            ->with(['appointments' => function($query) {
                $query->where('status', 'completed')
                      ->latest('scheduled_at')
                      ->limit(1);
            }])
            ->latest()
            ->get()
            ->map(function($vehicle) {
                $lastAppointment = $vehicle->appointments->first();
                return [
                    'id' => $vehicle->id,
                    'brand' => $vehicle->brand,
                    'model' => $vehicle->model,
                    'year' => $vehicle->year,
                    'color' => $vehicle->color,
                    'license_plate' => $vehicle->license_plate,
                    'vehicle_type' => $vehicle->vehicle_type,
                    'notes' => $vehicle->notes,
                    'created_at' => $vehicle->created_at,
                    'appointments_count' => $vehicle->appointments_count,
                    'last_appointment' => $lastAppointment ? $lastAppointment->scheduled_at : null,
                ];
            });

        return Inertia::render('Client/Vehicles/Index', [
            'vehicles' => $vehicles,
        ]);
    }

    /**
     * Show the form for creating a new vehicle.
     */
    public function create()
    {
        return Inertia::render('Client/Vehicles/Create');
    }

    /**
     * Store a newly created vehicle in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'brand' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'color' => 'required|string|max:255',
            'license_plate' => 'required|string|max:255|unique:vehicles',
            'vehicle_type' => 'required|in:car,suv,truck,van,motorcycle,other',
            'notes' => 'nullable|string|max:1000',
        ]);

        $validated['user_id'] = Auth::id();

        Vehicle::create($validated);

        return redirect()->route('client.vehicles.index')
            ->with('success', 'Véhicule ajouté avec succès !');
    }

    /**
     * Display the specified vehicle.
     */
    public function show(Vehicle $vehicle)
    {
        $this->authorize('view', $vehicle);

        // Charger les rendez-vous du véhicule
        $appointments = $vehicle->appointments()
            ->orderBy('scheduled_at', 'desc')
            ->get();

        // Calculer les statistiques
        $stats = [
            'total_appointments' => $appointments->count(),
            'completed_appointments' => $appointments->where('status', 'completed')->count(),
            'pending_appointments' => $appointments->where('status', 'pending')->count(),
            'total_spent' => $appointments->where('status', 'completed')->sum('price'),
            'last_appointment' => $appointments->where('status', 'completed')->first()?->scheduled_at,
        ];

        return Inertia::render('Client/Vehicles/Show', [
            'vehicle' => $vehicle,
            'appointments' => $appointments,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified vehicle.
     */
    public function edit(Vehicle $vehicle)
    {
        $this->authorize('update', $vehicle);

        return Inertia::render('Client/Vehicles/Edit', [
            'vehicle' => $vehicle,
        ]);
    }

    /**
     * Update the specified vehicle in storage.
     */
    public function update(Request $request, Vehicle $vehicle)
    {
        $this->authorize('update', $vehicle);

        $validated = $request->validate([
            'brand' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'color' => 'required|string|max:255',
            'license_plate' => 'required|string|max:255|unique:vehicles,license_plate,' . $vehicle->id,
            'vehicle_type' => 'required|in:car,suv,truck,van,motorcycle,other',
            'notes' => 'nullable|string|max:1000',
        ]);

        $vehicle->update($validated);

        return redirect()->route('client.vehicles.index')
            ->with('success', 'Véhicule modifié avec succès !');
    }

    /**
     * Remove the specified vehicle from storage.
     */
    public function destroy(Vehicle $vehicle)
    {
        $this->authorize('delete', $vehicle);

        // Vérifier s'il y a des rendez-vous associés
        $appointmentsCount = $vehicle->appointments()->count();

        if ($appointmentsCount > 0) {
            return redirect()->route('client.vehicles.show', $vehicle)
                ->with('error', 'Impossible de supprimer ce véhicule car il a des rendez-vous associés. Utilisez "Demander suppression" à la place.');
        }

        $vehicle->delete();

        return redirect()->route('client.vehicles.index')
            ->with('success', 'Véhicule supprimé avec succès !');
    }

    /**
     * Request deletion of a vehicle with appointments.
     */
    public function requestDeletion(Vehicle $vehicle)
    {
        $this->authorize('delete', $vehicle);

        // Ici, vous pourriez envoyer une notification à l'admin
        // ou marquer le véhicule pour suppression

        return redirect()->route('client.vehicles.show', $vehicle)
            ->with('info', 'Demande de suppression envoyée. Notre équipe vous contactera pour confirmer la suppression de ce véhicule et de ses rendez-vous associés.');
    }
}
