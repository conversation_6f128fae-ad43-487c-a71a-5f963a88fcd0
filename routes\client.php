<?php

use App\Http\Controllers\Client\AppointmentController;
use App\Http\Controllers\Client\DashboardController;
use App\Http\Controllers\Client\VehicleController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware(['auth', 'client'])->prefix('client')->name('client.')->group(function () {
    // Client Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Vehicles Management
    Route::resource('vehicles', VehicleController::class);
    Route::post('vehicles/{vehicle}/request-deletion', [VehicleController::class, 'requestDeletion'])->name('vehicles.request-deletion');

    // Appointments Management
    Route::resource('appointments', AppointmentController::class);
    Route::patch('appointments/{appointment}/cancel', [AppointmentController::class, 'cancel'])->name('appointments.cancel');
});
