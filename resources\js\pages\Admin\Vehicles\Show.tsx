import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { ArrowLeft, Car, User, Calendar, Clock, Phone, Mail } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';

interface Vehicle {
    id: number;
    brand: string;
    model: string;
    year: number;
    color: string;
    license_plate: string;
    vehicle_type: string;
    notes?: string;
    created_at: string;
    user: {
        id: number;
        name: string;
        email: string;
        phone?: string;
    };
    appointments: Array<{
        id: number;
        scheduled_at: string;
        service_type: string;
        status: string;
        price: number;
    }>;
}

interface Stats {
    total_appointments: number;
    completed_appointments: number;
    pending_appointments: number;
    total_revenue: number;
    last_appointment?: string;
}

interface ShowVehicleProps {
    vehicle: Vehicle;
    stats: Stats;
}

export default function ShowVehicle({ vehicle, stats }: ShowVehicleProps) {
    const getVehicleTypeLabel = (type: string) => {
        switch (type) {
            case 'car': return 'Voiture';
            case 'suv': return 'SUV';
            case 'truck': return 'Camion';
            case 'motorcycle': return 'Moto';
            case 'van': return 'Fourgon';
            default: return type;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed': return 'bg-green-100 text-green-800';
            case 'confirmed': return 'bg-blue-100 text-blue-800';
            case 'in_progress': return 'bg-yellow-100 text-yellow-800';
            case 'pending': return 'bg-orange-100 text-orange-800';
            case 'cancelled': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed': return 'Terminé';
            case 'confirmed': return 'Confirmé';
            case 'in_progress': return 'En cours';
            case 'pending': return 'En attente';
            case 'cancelled': return 'Annulé';
            default: return status;
        }
    };

    const getServiceLabel = (serviceType: string) => {
        switch (serviceType) {
            case 'basic': return 'Lavage Basique';
            case 'premium': return 'Lavage Premium';
            case 'deluxe': return 'Lavage Deluxe';
            default: return serviceType;
        }
    };

    return (
        <AppLayout>
            <Head title={`${vehicle.brand} ${vehicle.model} - Détails Véhicule`} />
            
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button asChild variant="outline" size="sm">
                        <Link href="/admin/vehicles">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Retour
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">
                            {vehicle.brand} {vehicle.model}
                        </h1>
                        <p className="text-gray-600">Détails complets du véhicule</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Informations du véhicule */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Car className="h-5 w-5" />
                                Informations du véhicule
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Type</span>
                                <Badge variant="secondary">
                                    {getVehicleTypeLabel(vehicle.vehicle_type)}
                                </Badge>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Marque et modèle</span>
                                <span className="text-sm font-medium">
                                    {vehicle.brand} {vehicle.model}
                                </span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Année</span>
                                <span className="text-sm">{vehicle.year}</span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Couleur</span>
                                <span className="text-sm">{vehicle.color}</span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Plaque</span>
                                <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                    {vehicle.license_plate}
                                </span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Ajouté le</span>
                                <span className="text-sm text-gray-600">
                                    {new Date(vehicle.created_at).toLocaleDateString('fr-FR')}
                                </span>
                            </div>

                            {vehicle.notes && (
                                <div className="border-t pt-4">
                                    <span className="text-sm font-medium text-gray-600">Notes</span>
                                    <p className="text-sm text-gray-700 mt-1">{vehicle.notes}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Informations du propriétaire */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Propriétaire
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Nom</span>
                                <span className="text-sm font-medium">{vehicle.user.name}</span>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Email</span>
                                <span className="text-sm">{vehicle.user.email}</span>
                            </div>
                            
                            {vehicle.user.phone && (
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Téléphone</span>
                                    <span className="text-sm">{vehicle.user.phone}</span>
                                </div>
                            )}

                            <div className="pt-4">
                                <Button asChild size="sm" className="w-full">
                                    <Link href={`/admin/users/${vehicle.user.id}`}>
                                        <User className="w-4 h-4 mr-2" />
                                        Voir le profil client
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Statistiques */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Calendar className="h-5 w-5" />
                            Statistiques
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                    {stats.total_appointments}
                                </div>
                                <p className="text-sm text-gray-600">Total RDV</p>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">
                                    {stats.completed_appointments}
                                </div>
                                <p className="text-sm text-gray-600">Terminés</p>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-orange-600">
                                    {stats.pending_appointments}
                                </div>
                                <p className="text-sm text-gray-600">En attente</p>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">
                                    {stats.total_revenue} DH
                                </div>
                                <p className="text-sm text-gray-600">Revenus</p>
                            </div>
                            <div className="text-center">
                                {stats.last_appointment ? (
                                    <>
                                        <div className="text-sm font-medium">
                                            {new Date(stats.last_appointment).toLocaleDateString('fr-FR')}
                                        </div>
                                        <p className="text-sm text-gray-600">Dernier lavage</p>
                                    </>
                                ) : (
                                    <>
                                        <div className="text-sm text-gray-400">-</div>
                                        <p className="text-sm text-gray-600">Dernier lavage</p>
                                    </>
                                )}
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Historique des rendez-vous */}
                <Card>
                    <CardHeader>
                        <CardTitle>Historique des rendez-vous ({vehicle.appointments.length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {vehicle.appointments.length > 0 ? (
                            <div className="space-y-3">
                                {vehicle.appointments.map((appointment) => (
                                    <div key={appointment.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <Clock className="h-4 w-4 text-gray-400" />
                                            <div>
                                                <p className="text-sm font-medium">
                                                    {getServiceLabel(appointment.service_type)} - {appointment.price} DH
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {new Date(appointment.scheduled_at).toLocaleDateString('fr-FR')} à{' '}
                                                    {new Date(appointment.scheduled_at).toLocaleTimeString('fr-FR', { 
                                                        hour: '2-digit', 
                                                        minute: '2-digit' 
                                                    })}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Badge className={getStatusColor(appointment.status)}>
                                                {getStatusText(appointment.status)}
                                            </Badge>
                                            <Button asChild size="sm" variant="outline">
                                                <Link href={`/admin/appointments/${appointment.id}`}>
                                                    Voir
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun rendez-vous</h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    Ce véhicule n'a pas encore de rendez-vous programmé.
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
