import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { ArrowLeft, Calendar, Car, Clock, MapPin, User, CreditCard, FileText, Plus } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';

interface Appointment {
    id: number;
    scheduled_at: string;
    service_type: string;
    status: string;
    price: number;
    notes?: string;
    admin_notes?: string;
    created_at: string;
    vehicle: {
        brand: string;
        model: string;
        year: number;
        color: string;
        license_plate: string;
        vehicle_type: string;
    };
}

interface ShowAppointmentProps {
    appointment: Appointment;
}

export default function ShowAppointment({ appointment }: ShowAppointmentProps) {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800';
            case 'in_progress':
                return 'bg-yellow-100 text-yellow-800';
            case 'pending':
                return 'bg-orange-100 text-orange-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed':
                return 'Terminé';
            case 'confirmed':
                return 'Confirmé';
            case 'in_progress':
                return 'En cours';
            case 'pending':
                return 'En attente';
            case 'cancelled':
                return 'Annulé';
            default:
                return status;
        }
    };

    const getServiceLabel = (serviceType: string) => {
        switch (serviceType) {
            case 'basic':
                return 'Lavage Basique (150 DH)';
            case 'premium':
                return 'Lavage Premium (250 DH)';
            case 'deluxe':
                return 'Lavage Deluxe (350 DH)';
            default:
                return serviceType;
        }
    };

    const getServiceDescription = (serviceType: string) => {
        switch (serviceType) {
            case 'basic':
                return 'Lavage extérieur + aspirateur';
            case 'premium':
                return 'Lavage complet + cire + pneus';
            case 'deluxe':
                return 'Lavage premium + intérieur + détailing';
            default:
                return '';
        }
    };

    return (
        <AppLayout>
            <Head title={`Rendez-vous #${appointment.id} - AutoWash`} />

            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button asChild variant="outline" size="sm">
                        <Link href="/client/appointments">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Retour
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Rendez-vous #{appointment.id}</h1>
                        <p className="text-gray-600">Détails de votre rendez-vous de lavage</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Informations du rendez-vous */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Informations du rendez-vous
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Statut</span>
                                <Badge className={getStatusColor(appointment.status)}>
                                    {getStatusText(appointment.status)}
                                </Badge>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Service</span>
                                <div className="text-right">
                                    <div className="text-sm font-medium">{getServiceLabel(appointment.service_type)}</div>
                                    <div className="text-xs text-gray-500">{getServiceDescription(appointment.service_type)}</div>
                                </div>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Prix</span>
                                <span className="text-lg font-bold text-green-600">{appointment.price} DH</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Date et heure</span>
                                <div className="text-right">
                                    <div className="text-sm font-medium">
                                        {new Date(appointment.scheduled_at).toLocaleDateString('fr-FR')}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        {new Date(appointment.scheduled_at).toLocaleTimeString('fr-FR', {
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        })}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Créé le</span>
                                <span className="text-sm text-gray-600">
                                    {new Date(appointment.created_at).toLocaleDateString('fr-FR')}
                                </span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Informations du véhicule */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Car className="h-5 w-5" />
                                Véhicule
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Marque et modèle</span>
                                <span className="text-sm font-medium">
                                    {appointment.vehicle.brand} {appointment.vehicle.model}
                                </span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Année</span>
                                <span className="text-sm">{appointment.vehicle.year}</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Couleur</span>
                                <span className="text-sm">{appointment.vehicle.color}</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Plaque</span>
                                <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                    {appointment.vehicle.license_plate}
                                </span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Type</span>
                                <span className="text-sm capitalize">{appointment.vehicle.vehicle_type}</span>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Notes */}
                {(appointment.notes || appointment.admin_notes) && (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {appointment.notes && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <FileText className="h-5 w-5" />
                                        Vos notes
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-sm text-gray-700">{appointment.notes}</p>
                                </CardContent>
                            </Card>
                        )}

                        {appointment.admin_notes && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        Notes de l'équipe
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-sm text-gray-700">{appointment.admin_notes}</p>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                )}

                {/* Actions */}
                <Card>
                    <CardContent className="pt-6">
                        <div className="flex flex-wrap gap-4">
                            <Button asChild>
                                <Link href="/client/appointments">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Retour à la liste
                                </Link>
                            </Button>
                            <Button asChild variant="outline">
                                <Link href="/client/appointments/create">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Nouveau rendez-vous
                                </Link>
                            </Button>
                            {appointment.status === 'pending' && (
                                <Button variant="outline" className="text-orange-600 hover:text-orange-700">
                                    <Clock className="mr-2 h-4 w-4" />
                                    En attente de confirmation
                                </Button>
                            )}
                            {appointment.status === 'confirmed' && (
                                <Button variant="outline" className="text-blue-600 hover:text-blue-700">
                                    <Calendar className="mr-2 h-4 w-4" />
                                    Rendez-vous confirmé
                                </Button>
                            )}
                            {appointment.status === 'completed' && (
                                <Button variant="outline" className="text-green-600 hover:text-green-700">
                                    ✓ Lavage terminé
                                </Button>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
