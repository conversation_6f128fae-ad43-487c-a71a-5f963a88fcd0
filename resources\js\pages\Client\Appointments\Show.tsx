import { Head, <PERSON>, router } from '@inertiajs/react';
import { ArrowLeft, Calendar, Car, Clock, MapPin, User, CreditCard, FileText, Plus, Edit, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';

interface Appointment {
    id: number;
    scheduled_at: string;
    service_type: string;
    status: string;
    price: number;
    notes?: string;
    admin_notes?: string;
    created_at: string;
    vehicle: {
        brand: string;
        model: string;
        year: number;
        color: string;
        license_plate: string;
        vehicle_type: string;
    };
}

interface ShowAppointmentProps {
    appointment: Appointment;
}

export default function ShowAppointment({ appointment }: ShowAppointmentProps) {
    const handleCancelAppointment = () => {
        if (confirm('Êtes-vous sûr de vouloir annuler ce rendez-vous ?')) {
            router.delete(`/client/appointments/${appointment.id}`, {
                onSuccess: () => {
                    // Redirection sera gérée par le contrôleur
                },
                onError: (errors) => {
                    console.error('Erreur lors de l\'annulation:', errors);
                }
            });
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800';
            case 'in_progress':
                return 'bg-yellow-100 text-yellow-800';
            case 'pending':
                return 'bg-orange-100 text-orange-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed':
                return 'Terminé';
            case 'confirmed':
                return 'Confirmé';
            case 'in_progress':
                return 'En cours';
            case 'pending':
                return 'En attente';
            case 'cancelled':
                return 'Annulé';
            default:
                return status;
        }
    };

    const getServiceLabel = (serviceType: string) => {
        switch (serviceType) {
            case 'basic':
                return 'Lavage Basique (150 DH)';
            case 'premium':
                return 'Lavage Premium (250 DH)';
            case 'deluxe':
                return 'Lavage Deluxe (350 DH)';
            default:
                return serviceType;
        }
    };

    const getServiceDescription = (serviceType: string) => {
        switch (serviceType) {
            case 'basic':
                return 'Lavage extérieur + aspirateur';
            case 'premium':
                return 'Lavage complet + cire + pneus';
            case 'deluxe':
                return 'Lavage premium + intérieur + détailing';
            default:
                return '';
        }
    };

    return (
        <AppLayout>
            <Head title={`Rendez-vous #${appointment.id} - AutoWash`} />

            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button asChild variant="outline" size="sm">
                        <Link href="/client/appointments">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Retour
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Rendez-vous #{appointment.id}</h1>
                        <p className="text-gray-600">Détails de votre rendez-vous de lavage</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Informations du rendez-vous */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Informations du rendez-vous
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Statut</span>
                                <Badge className={getStatusColor(appointment.status)}>
                                    {getStatusText(appointment.status)}
                                </Badge>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Service</span>
                                <div className="text-right">
                                    <div className="text-sm font-medium">{getServiceLabel(appointment.service_type)}</div>
                                    <div className="text-xs text-gray-500">{getServiceDescription(appointment.service_type)}</div>
                                </div>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Prix</span>
                                <span className="text-lg font-bold text-green-600">{appointment.price} DH</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Date et heure</span>
                                <div className="text-right">
                                    <div className="text-sm font-medium">
                                        {new Date(appointment.scheduled_at).toLocaleDateString('fr-FR')}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        {new Date(appointment.scheduled_at).toLocaleTimeString('fr-FR', {
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        })}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Créé le</span>
                                <span className="text-sm text-gray-600">
                                    {new Date(appointment.created_at).toLocaleDateString('fr-FR')}
                                </span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Informations du véhicule */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Car className="h-5 w-5" />
                                Véhicule
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Marque et modèle</span>
                                <span className="text-sm font-medium">
                                    {appointment.vehicle.brand} {appointment.vehicle.model}
                                </span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Année</span>
                                <span className="text-sm">{appointment.vehicle.year}</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Couleur</span>
                                <span className="text-sm">{appointment.vehicle.color}</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Plaque</span>
                                <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                    {appointment.vehicle.license_plate}
                                </span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-600">Type</span>
                                <span className="text-sm capitalize">{appointment.vehicle.vehicle_type}</span>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Notes */}
                {(appointment.notes || appointment.admin_notes) && (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {appointment.notes && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <FileText className="h-5 w-5" />
                                        Vos notes
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-sm text-gray-700">{appointment.notes}</p>
                                </CardContent>
                            </Card>
                        )}

                        {appointment.admin_notes && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        Notes de l'équipe
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-sm text-gray-700">{appointment.admin_notes}</p>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                )}

                {/* Actions */}
                <Card>
                    <CardContent className="pt-6">
                        <div className="space-y-4">
                            {/* Actions principales */}
                            <div className="flex flex-wrap gap-4">
                                <Button asChild>
                                    <Link href="/client/appointments">
                                        <ArrowLeft className="mr-2 h-4 w-4" />
                                        Retour à la liste
                                    </Link>
                                </Button>
                                <Button asChild variant="outline">
                                    <Link href="/client/appointments/create">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Nouveau rendez-vous
                                    </Link>
                                </Button>
                            </div>

                            {/* Actions selon le statut */}
                            {appointment.status === 'pending' && (
                                <div className="border-t pt-4">
                                    <p className="text-sm text-gray-600 mb-3">
                                        Ce rendez-vous est en attente de confirmation. Vous pouvez encore le modifier ou l'annuler.
                                    </p>
                                    <div className="flex flex-wrap gap-3">
                                        <Button asChild size="sm">
                                            <Link href={`/client/appointments/${appointment.id}/edit`}>
                                                <Edit className="mr-2 h-4 w-4" />
                                                Modifier
                                            </Link>
                                        </Button>
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            className="text-red-600 hover:text-red-700 hover:border-red-300"
                                            onClick={handleCancelAppointment}
                                        >
                                            <Trash2 className="mr-2 h-4 w-4" />
                                            Annuler le rendez-vous
                                        </Button>
                                    </div>
                                </div>
                            )}

                            {appointment.status === 'confirmed' && (
                                <div className="border-t pt-4">
                                    <div className="flex items-center gap-2 text-blue-600">
                                        <Calendar className="h-4 w-4" />
                                        <span className="text-sm font-medium">Rendez-vous confirmé par notre équipe</span>
                                    </div>
                                    <p className="text-sm text-gray-600 mt-1">
                                        Votre rendez-vous a été confirmé. Contactez-nous si vous avez besoin de modifications.
                                    </p>
                                </div>
                            )}

                            {appointment.status === 'in_progress' && (
                                <div className="border-t pt-4">
                                    <div className="flex items-center gap-2 text-yellow-600">
                                        <Clock className="h-4 w-4" />
                                        <span className="text-sm font-medium">Lavage en cours</span>
                                    </div>
                                    <p className="text-sm text-gray-600 mt-1">
                                        Notre équipe s'occupe actuellement de votre véhicule.
                                    </p>
                                </div>
                            )}

                            {appointment.status === 'completed' && (
                                <div className="border-t pt-4">
                                    <div className="flex items-center gap-2 text-green-600">
                                        <span className="text-sm font-medium">✓ Lavage terminé</span>
                                    </div>
                                    <p className="text-sm text-gray-600 mt-1">
                                        Merci d'avoir choisi AutoWash ! N'hésitez pas à prendre un nouveau rendez-vous.
                                    </p>
                                </div>
                            )}

                            {appointment.status === 'cancelled' && (
                                <div className="border-t pt-4">
                                    <div className="flex items-center gap-2 text-red-600">
                                        <span className="text-sm font-medium">✗ Rendez-vous annulé</span>
                                    </div>
                                    <p className="text-sm text-gray-600 mt-1">
                                        Ce rendez-vous a été annulé.
                                    </p>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
