<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use App\Models\Vehicle;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class AppointmentController extends Controller
{
    use AuthorizesRequests;
    /**
     * Display a listing of the user's appointments.
     */
    public function index()
    {
        $appointments = Appointment::where('user_id', Auth::id())
            ->with('vehicle')
            ->latest('scheduled_at')
            ->get();

        return Inertia::render('Client/Appointments/Index', [
            'appointments' => $appointments,
        ]);
    }

    /**
     * Show the form for creating a new appointment.
     */
    public function create()
    {
        $vehicles = Vehicle::where('user_id', Auth::id())->get();

        if ($vehicles->isEmpty()) {
            return redirect()->route('client.vehicles.create')
                ->with('info', 'Vous devez d\'abord ajouter un véhicule avant de prendre rendez-vous.');
        }

        return Inertia::render('Client/Appointments/Create', [
            'vehicles' => $vehicles,
            'serviceTypes' => Appointment::SERVICE_TYPES,
            'servicePrices' => Appointment::SERVICE_PRICES,
            'serviceDurations' => Appointment::SERVICE_DURATIONS,
        ]);
    }

    /**
     * Store a newly created appointment in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'vehicle_id' => 'required|exists:vehicles,id',
            'service_type' => 'required|in:' . implode(',', array_keys(Appointment::SERVICE_TYPES)),
            'scheduled_at' => 'required|date|after:now',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Verify the vehicle belongs to the authenticated user
        $vehicle = Vehicle::where('id', $validated['vehicle_id'])
            ->where('user_id', auth()->id())
            ->firstOrFail();

        // Set price and duration based on service type
        $validated['user_id'] = Auth::id();
        $validated['price'] = Appointment::SERVICE_PRICES[$validated['service_type']];
        $validated['duration'] = Appointment::SERVICE_DURATIONS[$validated['service_type']];

        Appointment::create($validated);

        return redirect()->route('client.appointments.index')
            ->with('success', 'Rendez-vous pris avec succès ! Nous vous confirmerons bientôt.');
    }

    /**
     * Display the specified appointment.
     */
    public function show(Appointment $appointment)
    {
        $this->authorize('view', $appointment);

        $appointment->load('vehicle');

        return Inertia::render('Client/Appointments/Show', [
            'appointment' => $appointment,
        ]);
    }

    /**
     * Show the form for editing the specified appointment.
     */
    public function edit(Appointment $appointment)
    {
        $this->authorize('update', $appointment);

        // Only allow editing if appointment is pending
        if ($appointment->status !== Appointment::STATUS_PENDING) {
            return redirect()->route('client.appointments.show', $appointment)
                ->with('error', 'Vous ne pouvez modifier que les rendez-vous en attente.');
        }

        $vehicles = auth()->user()->vehicles()->get();

        return Inertia::render('Client/Appointments/Edit', [
            'appointment' => $appointment,
            'vehicles' => $vehicles,
            'serviceTypes' => Appointment::SERVICE_TYPES,
            'servicePrices' => Appointment::SERVICE_PRICES,
            'serviceDurations' => Appointment::SERVICE_DURATIONS,
        ]);
    }

    /**
     * Update the specified appointment in storage.
     */
    public function update(Request $request, Appointment $appointment)
    {
        $this->authorize('update', $appointment);

        // Only allow updating if appointment is pending
        if ($appointment->status !== Appointment::STATUS_PENDING) {
            return redirect()->route('client.appointments.show', $appointment)
                ->with('error', 'Vous ne pouvez modifier que les rendez-vous en attente.');
        }

        $validated = $request->validate([
            'vehicle_id' => 'required|exists:vehicles,id',
            'service_type' => 'required|in:' . implode(',', array_keys(Appointment::SERVICE_TYPES)),
            'scheduled_at' => 'required|date|after:now',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Verify the vehicle belongs to the authenticated user
        $vehicle = Vehicle::where('id', $validated['vehicle_id'])
            ->where('user_id', auth()->id())
            ->firstOrFail();

        // Update price and duration based on service type
        $validated['price'] = Appointment::SERVICE_PRICES[$validated['service_type']];
        $validated['duration'] = Appointment::SERVICE_DURATIONS[$validated['service_type']];

        $appointment->update($validated);

        return redirect()->route('client.appointments.index')
            ->with('success', 'Rendez-vous modifié avec succès !');
    }

    /**
     * Cancel the specified appointment.
     */
    public function cancel(Appointment $appointment)
    {
        $this->authorize('update', $appointment);

        // Only allow cancelling if appointment is not completed
        if ($appointment->status === Appointment::STATUS_COMPLETED) {
            return redirect()->route('client.appointments.show', $appointment)
                ->with('error', 'Vous ne pouvez pas annuler un rendez-vous terminé.');
        }

        $appointment->update(['status' => Appointment::STATUS_CANCELLED]);

        return redirect()->route('client.appointments.index')
            ->with('success', 'Rendez-vous annulé avec succès.');
    }
}
