<?php

use App\Http\Controllers\Admin\AppointmentController;
use App\Http\Controllers\Admin\DashboardController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Appointments Management
    Route::resource('appointments', AppointmentController::class);
    Route::patch('appointments/{appointment}/status', [AppointmentController::class, 'updateStatus'])->name('appointments.update-status');

    // Routes temporaires pour éviter les erreurs 404
    Route::get('users', function () {
        return redirect()->route('admin.dashboard')->with('info', 'Gestion des clients - En cours de développement');
    })->name('users.index');

    Route::get('vehicles', function () {
        return redirect()->route('admin.dashboard')->with('info', 'Gestion des véhicules - En cours de développement');
    })->name('vehicles.index');

    Route::get('stats', function () {
        return redirect()->route('admin.dashboard')->with('info', 'Statistiques avancées - En cours de développement');
    })->name('stats.index');

    Route::get('settings', function () {
        return redirect()->route('admin.dashboard')->with('info', 'Paramètres - En cours de développement');
    })->name('settings.index');
});
