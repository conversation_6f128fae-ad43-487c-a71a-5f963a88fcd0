import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { BarChart3, BookOpen, Calendar, Car, Folder, LayoutGrid, Search, Tags, Users, Settings, ClipboardList } from 'lucide-react';
import AppLogo from './app-logo';

// Menu pour les clients
const clientNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/client/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Mes Véhicules',
        href: '/client/vehicles',
        icon: Car,
    },
    {
        title: '<PERSON><PERSON>dez-vous',
        href: '/client/appointments',
        icon: ClipboardList,
    },
    {
        title: 'Calendrier',
        href: '/client/calendar',
        icon: Calendar,
    },
];

// Menu pour les admins
const adminNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Rendez-vous',
        href: '/admin/appointments',
        icon: ClipboardList,
    },
];

const footerNavItems: NavItem[] = [
    {
        title: 'Support',
        href: '/support',
        icon: BookOpen,
    },
];

export function AppSidebar() {
    const { auth, ziggy } = usePage().props as any;
    const user = auth?.user;
    const currentUrl = ziggy?.location || '';

    // Déterminer le rôle de l'utilisateur et les éléments de navigation appropriés
    // Utiliser le rôle de l'utilisateur ou détecter depuis l'URL
    const isAdmin = user?.role === 'admin' || currentUrl.includes('/admin');
    const navItems = isAdmin ? adminNavItems : clientNavItems;
    const dashboardUrl = isAdmin ? '/admin/dashboard' : '/client/dashboard';

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href={dashboardUrl} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                {/* Indicateur d'espace */}
                <div className="px-3 py-2 mb-2">
                    <div className="text-xs font-medium text-sidebar-foreground/70 uppercase tracking-wider">
                        {isAdmin ? 'Espace Administrateur' : 'Espace Client'}
                    </div>
                </div>
                <NavMain items={navItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
