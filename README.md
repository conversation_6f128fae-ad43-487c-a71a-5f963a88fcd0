# SmartCalendar

Une application de calendrier intelligente construite avec <PERSON>, React, et Inertia.js.

## 🚀 Fonctionnalités

### ✅ Fonctionnalités principales
- **Gestion des événements** : <PERSON><PERSON><PERSON>, modifier, supprimer des événements
- **Calendriers multiples** : Organiser les événements par calendriers (personnel, travail, etc.)
- **Catégories** : Classer les événements par catégories avec des couleurs personnalisées
- **Vues multiples** : Vue mensuelle, hebdomadaire et quotidienne
- **Récurrence** : Événements récurrents (quotidien, hebdomadaire, mensuel, annuel)
- **Événements toute la journée** : Support des événements d'une journée complète
- **Priorités** : Définir la priorité des événements (faible, moyenne, élevée)
- **Statuts** : Gérer le statut des événements (confirmé, provisoire, annulé)
- **Localisation** : Ajouter des lieux aux événements

### 🎨 Interface utilisateur
- **Design moderne** : Interface utilisateur élégante avec Tailwind CSS
- **Composants réutilisables** : Basé sur shadcn/ui
- **Responsive** : Optimisé pour desktop et mobile
- **Mode sombre/clair** : Support des thèmes
- **Navigation intuitive** : Sidebar avec navigation facile

### 🔐 Sécurité et authentification
- **Authentification complète** : Inscription, connexion, réinitialisation de mot de passe
- **Autorisation** : Chaque utilisateur ne voit que ses propres données
- **Validation** : Validation côté client et serveur
- **Protection CSRF** : Sécurité contre les attaques CSRF

## 🛠️ Technologies utilisées

### Backend
- **Laravel 11** : Framework PHP moderne
- **SQLite** : Base de données légère pour le développement
- **Eloquent ORM** : Gestion des données avec des relations
- **Policies** : Autorisation granulaire

### Frontend
- **React 18** : Bibliothèque JavaScript moderne
- **TypeScript** : Typage statique pour plus de robustesse
- **Inertia.js** : SPA sans API avec Laravel
- **Tailwind CSS** : Framework CSS utilitaire
- **shadcn/ui** : Composants UI modernes
- **Lucide React** : Icônes élégantes

### Outils de développement
- **Vite** : Build tool rapide
- **ESLint** : Linting JavaScript/TypeScript
- **Prettier** : Formatage de code

## 📦 Installation

### Prérequis
- PHP 8.2 ou supérieur
- Composer
- Node.js 18 ou supérieur
- npm ou yarn

### Installation automatique

#### Windows
```bash
setup-smartcalendar.bat
```

#### Linux/Mac
```bash
chmod +x setup-smartcalendar.sh
./setup-smartcalendar.sh
```

### Installation manuelle

1. **Cloner le projet**
```bash
git clone <repository-url>
cd smartcalendar
```

2. **Installer les dépendances PHP**
```bash
composer install
```

3. **Installer les dépendances Node.js**
```bash
npm install
```

4. **Configuration de l'environnement**
```bash
cp .env.example .env
php artisan key:generate
```

5. **Base de données**
```bash
php artisan migrate:fresh --seed
```

6. **Build des assets**
```bash
npm run build
```

## 🚀 Démarrage

### Développement

1. **Démarrer le serveur Laravel**
```bash
php artisan serve
```

2. **Démarrer le serveur de développement frontend** (dans un autre terminal)
```bash
npm run dev
```

3. **Accéder à l'application**
Ouvrir http://localhost:8000 dans votre navigateur

### Connexion par défaut
- **Email** : <EMAIL>
- **Password** : password

## 📁 Structure du projet

```
smartcalendar/
├── app/
│   ├── Http/Controllers/          # Contrôleurs
│   │   ├── CalendarController.php
│   │   ├── CategoryController.php
│   │   └── EventController.php
│   ├── Models/                    # Modèles Eloquent
│   │   ├── Calendar.php
│   │   ├── Category.php
│   │   ├── Event.php
│   │   └── User.php
│   └── Policies/                  # Politiques d'autorisation
│       ├── CalendarPolicy.php
│       ├── CategoryPolicy.php
│       └── EventPolicy.php
├── database/
│   ├── migrations/                # Migrations de base de données
│   └── seeders/                   # Seeders pour les données de test
├── resources/
│   ├── js/
│   │   ├── components/            # Composants React
│   │   │   ├── calendar/          # Composants spécifiques au calendrier
│   │   │   └── ui/                # Composants UI réutilisables
│   │   ├── pages/                 # Pages de l'application
│   │   │   ├── Calendar/
│   │   │   ├── Categories/
│   │   │   └── Events/
│   │   └── types/                 # Types TypeScript
│   └── css/                       # Styles CSS
└── routes/
    └── web.php                    # Routes de l'application
```

## 🎯 Utilisation

### Créer un événement
1. Cliquer sur "Nouvel événement" depuis le dashboard ou le calendrier
2. Remplir les informations de l'événement
3. Sélectionner un calendrier et une catégorie (optionnel)
4. Configurer la récurrence si nécessaire
5. Enregistrer

### Gérer les calendriers
1. Aller dans "Calendriers" depuis la sidebar
2. Créer de nouveaux calendriers avec des couleurs personnalisées
3. Définir un calendrier par défaut
4. Gérer la visibilité (public/privé)

### Organiser avec les catégories
1. Aller dans "Catégories" depuis la sidebar
2. Créer des catégories avec des couleurs distinctives
3. Assigner des catégories aux événements pour une meilleure organisation

### Navigation du calendrier
- **Vue mensuelle** : Vue d'ensemble du mois
- **Vue hebdomadaire** : Détail de la semaine
- **Vue quotidienne** : Planning détaillé du jour
- **Filtres** : Afficher/masquer des calendriers spécifiques

## 🔧 Configuration

### Base de données
Par défaut, l'application utilise SQLite pour le développement. Pour utiliser MySQL ou PostgreSQL :

1. Modifier le fichier `.env`
2. Configurer les paramètres de base de données
3. Exécuter les migrations : `php artisan migrate:fresh --seed`

### Personnalisation
- **Couleurs** : Modifier les couleurs dans `tailwind.config.js`
- **Composants** : Personnaliser les composants dans `resources/js/components/`
- **Styles** : Ajuster les styles dans `resources/css/app.css`

## 🧪 Tests

```bash
# Tests PHP
php artisan test

# Tests JavaScript (si configurés)
npm run test
```

## 📝 API

L'application expose des endpoints API pour les requêtes AJAX :

- `GET /api/events` - Récupérer les événements
- `GET /api/calendars` - Récupérer les calendriers
- `GET /api/categories` - Récupérer les catégories

## 🤝 Contribution

1. Fork le projet
2. Créer une branche pour votre fonctionnalité (`git checkout -b feature/AmazingFeature`)
3. Commit vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

Pour obtenir de l'aide :
1. Consulter la documentation
2. Ouvrir une issue sur GitHub
3. Contacter l'équipe de développement

## 🎉 Remerciements

- Laravel pour le framework backend robuste
- React pour l'interface utilisateur moderne
- Inertia.js pour l'intégration seamless
- shadcn/ui pour les composants UI élégants
- Tailwind CSS pour le styling efficace
