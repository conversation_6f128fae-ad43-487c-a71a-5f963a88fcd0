<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class StatsController extends Controller
{
    /**
     * Display statistics dashboard.
     */
    public function index(Request $request)
    {
        // Période par défaut : ce mois
        $period = $request->get('period', 'month');
        
        // Statistiques générales
        $generalStats = [
            'total_clients' => User::where('role', 'client')->count(),
            'total_vehicles' => Vehicle::count(),
            'total_appointments' => Appointment::count(),
            'total_revenue' => Appointment::where('status', 'completed')->sum('price'),
        ];

        // Revenus par mois (12 derniers mois)
        $monthlyRevenue = Appointment::where('status', 'completed')
            ->where('scheduled_at', '>=', now()->subMonths(12))
            ->select(
                DB::raw('YEAR(scheduled_at) as year'),
                DB::raw('MONTH(scheduled_at) as month'),
                DB::raw('SUM(price) as revenue'),
                DB::raw('COUNT(*) as appointments_count')
            )
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'period' => sprintf('%04d-%02d', $item->year, $item->month),
                    'revenue' => $item->revenue,
                    'appointments' => $item->appointments_count,
                ];
            });

        // Services les plus populaires
        $popularServices = Appointment::select('service_type', DB::raw('COUNT(*) as count'))
            ->groupBy('service_type')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($item) {
                $labels = [
                    'basic' => 'Lavage Basique',
                    'premium' => 'Lavage Premium',
                    'deluxe' => 'Lavage Deluxe',
                ];
                return [
                    'service' => $labels[$item->service_type] ?? $item->service_type,
                    'count' => $item->count,
                ];
            });

        // Clients les plus actifs
        $topClients = User::where('role', 'client')
            ->withCount('appointments')
            ->with(['appointments' => function($q) {
                $q->where('status', 'completed');
            }])
            ->having('appointments_count', '>', 0)
            ->orderBy('appointments_count', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'appointments_count' => $user->appointments_count,
                    'total_spent' => $user->appointments->sum('price'),
                ];
            });

        // Évolution des rendez-vous par statut
        $appointmentsByStatus = Appointment::select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get()
            ->map(function ($item) {
                $labels = [
                    'pending' => 'En attente',
                    'confirmed' => 'Confirmé',
                    'in_progress' => 'En cours',
                    'completed' => 'Terminé',
                    'cancelled' => 'Annulé',
                ];
                return [
                    'status' => $labels[$item->status] ?? $item->status,
                    'count' => $item->count,
                ];
            });

        return Inertia::render('Admin/Stats/Index', [
            'generalStats' => $generalStats,
            'monthlyRevenue' => $monthlyRevenue,
            'popularServices' => $popularServices,
            'topClients' => $topClients,
            'appointmentsByStatus' => $appointmentsByStatus,
            'period' => $period,
        ]);
    }
}
