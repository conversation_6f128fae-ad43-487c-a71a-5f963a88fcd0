<?php

namespace Database\Seeders;

use App\Models\Calendar;
use App\Models\Category;
use App\Models\Event;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class CalendarSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user or create one
        $user = User::first();
        if (!$user) {
            $user = User::factory()->create([
                'name' => 'Demo User',
                'email' => '<EMAIL>',
            ]);
        }

        // Create default calendar
        $defaultCalendar = Calendar::create([
            'name' => 'Mon Calendrier',
            'description' => 'Calendrier principal',
            'color' => '#3B82F6',
            'is_default' => true,
            'is_public' => false,
            'user_id' => $user->id,
        ]);

        // Create work calendar
        $workCalendar = Calendar::create([
            'name' => 'Travail',
            'description' => 'Événements professionnels',
            'color' => '#EF4444',
            'is_default' => false,
            'is_public' => false,
            'user_id' => $user->id,
        ]);

        // Create personal calendar
        $personalCalendar = Calendar::create([
            'name' => 'Personnel',
            'description' => 'Événements personnels',
            'color' => '#10B981',
            'is_default' => false,
            'is_public' => false,
            'user_id' => $user->id,
        ]);

        // Create categories
        $workCategory = Category::create([
            'name' => 'Réunions',
            'color' => '#F59E0B',
            'description' => 'Réunions et rendez-vous professionnels',
            'user_id' => $user->id,
        ]);

        $personalCategory = Category::create([
            'name' => 'Loisirs',
            'color' => '#8B5CF6',
            'description' => 'Activités de loisirs et détente',
            'user_id' => $user->id,
        ]);

        $healthCategory = Category::create([
            'name' => 'Santé',
            'color' => '#EC4899',
            'description' => 'Rendez-vous médicaux et santé',
            'user_id' => $user->id,
        ]);

        // Create sample events
        $events = [
            [
                'title' => 'Réunion équipe',
                'description' => 'Réunion hebdomadaire de l\'équipe de développement',
                'start_date' => Carbon::now()->addDays(1)->setHour(9)->setMinute(0),
                'end_date' => Carbon::now()->addDays(1)->setHour(10)->setMinute(30),
                'location' => 'Salle de conférence A',
                'calendar_id' => $workCalendar->id,
                'category_id' => $workCategory->id,
                'priority' => 'high',
            ],
            [
                'title' => 'Rendez-vous médecin',
                'description' => 'Consultation de routine',
                'start_date' => Carbon::now()->addDays(3)->setHour(14)->setMinute(0),
                'end_date' => Carbon::now()->addDays(3)->setHour(15)->setMinute(0),
                'location' => 'Cabinet médical',
                'calendar_id' => $personalCalendar->id,
                'category_id' => $healthCategory->id,
                'priority' => 'medium',
            ],
            [
                'title' => 'Cinéma avec amis',
                'description' => 'Sortie cinéma pour voir le nouveau film',
                'start_date' => Carbon::now()->addDays(5)->setHour(20)->setMinute(0),
                'end_date' => Carbon::now()->addDays(5)->setHour(23)->setMinute(0),
                'location' => 'Cinéma Pathé',
                'calendar_id' => $personalCalendar->id,
                'category_id' => $personalCategory->id,
                'priority' => 'low',
            ],
            [
                'title' => 'Formation Laravel',
                'description' => 'Session de formation sur les nouvelles fonctionnalités Laravel',
                'start_date' => Carbon::now()->addDays(7)->setHour(9)->setMinute(0),
                'end_date' => Carbon::now()->addDays(7)->setHour(17)->setMinute(0),
                'location' => 'Centre de formation',
                'calendar_id' => $workCalendar->id,
                'category_id' => $workCategory->id,
                'priority' => 'high',
                'all_day' => false,
            ],
            [
                'title' => 'Anniversaire Marie',
                'description' => 'Fête d\'anniversaire de Marie',
                'start_date' => Carbon::now()->addDays(10)->startOfDay(),
                'end_date' => Carbon::now()->addDays(10)->endOfDay(),
                'location' => 'Restaurant Le Gourmet',
                'calendar_id' => $personalCalendar->id,
                'category_id' => $personalCategory->id,
                'priority' => 'medium',
                'all_day' => true,
            ],
            [
                'title' => 'Réunion client',
                'description' => 'Présentation du projet SmartCalendar',
                'start_date' => Carbon::now()->addDays(2)->setHour(15)->setMinute(0),
                'end_date' => Carbon::now()->addDays(2)->setHour(16)->setMinute(30),
                'location' => 'Bureau client',
                'calendar_id' => $workCalendar->id,
                'category_id' => $workCategory->id,
                'priority' => 'high',
                'recurrence_type' => 'weekly',
                'recurrence_interval' => 1,
                'recurrence_end_date' => Carbon::now()->addMonths(3),
            ],
        ];

        foreach ($events as $eventData) {
            Event::create(array_merge($eventData, [
                'user_id' => $user->id,
                'status' => 'confirmed',
            ]));
        }
    }
}
