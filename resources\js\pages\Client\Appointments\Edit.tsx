import { Head, <PERSON>, useForm } from '@inertiajs/react';
import { ArrowLeft, Calendar, Car } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';

interface Vehicle {
    id: number;
    brand: string;
    model: string;
    license_plate: string;
}

interface Appointment {
    id: number;
    vehicle_id: number;
    service_type: string;
    scheduled_at: string;
    notes?: string;
    status: string;
}

interface EditAppointmentProps {
    appointment: Appointment;
    vehicles: Vehicle[];
}

export default function EditAppointment({ appointment, vehicles = [] }: EditAppointmentProps) {
    const { data, setData, put, processing, errors } = useForm({
        vehicle_id: appointment.vehicle_id.toString(),
        service_type: appointment.service_type,
        scheduled_at: appointment.scheduled_at.slice(0, 16), // Format pour datetime-local
        notes: appointment.notes || '',
    });

    const submit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/client/appointments/${appointment.id}`);
    };

    const services = [
        { value: 'basic', label: 'Lavage Basique', price: 150, description: 'Lavage extérieur + aspirateur' },
        { value: 'premium', label: 'Lavage Premium', price: 250, description: 'Lavage complet + cire + pneus' },
        { value: 'deluxe', label: 'Lavage Deluxe', price: 350, description: 'Lavage premium + intérieur + détailing' },
    ];

    // Date minimum : maintenant
    const minDate = new Date().toISOString().slice(0, 16);

    // Vérifier si le RDV peut être modifié
    const canEdit = appointment.status === 'pending';

    if (!canEdit) {
        return (
            <AppLayout>
                <Head title="Modifier le rendez-vous - AutoWash" />
                
                <div className="space-y-6">
                    <div className="flex items-center gap-4">
                        <Button asChild variant="outline" size="sm">
                            <Link href={`/client/appointments/${appointment.id}`}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Retour
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">Modification impossible</h1>
                            <p className="text-gray-600">Ce rendez-vous ne peut plus être modifié</p>
                        </div>
                    </div>

                    <Card>
                        <CardContent className="text-center py-12">
                            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Rendez-vous confirmé</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Ce rendez-vous a été confirmé par notre équipe et ne peut plus être modifié.
                                Contactez-nous si vous avez besoin de faire des changements.
                            </p>
                            <div className="mt-6">
                                <Button asChild>
                                    <Link href={`/client/appointments/${appointment.id}`}>
                                        Voir les détails
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout>
            <Head title="Modifier le rendez-vous - AutoWash" />
            
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button asChild variant="outline" size="sm">
                        <Link href={`/client/appointments/${appointment.id}`}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Retour
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Modifier le rendez-vous</h1>
                        <p className="text-gray-600">Modifiez les détails de votre rendez-vous</p>
                    </div>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Calendar className="h-5 w-5" />
                            Détails du rendez-vous
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={submit} className="space-y-6">
                            <div className="space-y-2">
                                <Label htmlFor="vehicle_id">Véhicule</Label>
                                <Select value={data.vehicle_id} onValueChange={(value) => setData('vehicle_id', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Sélectionnez un véhicule" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {vehicles.map((vehicle) => (
                                            <SelectItem key={vehicle.id} value={vehicle.id.toString()}>
                                                {vehicle.brand} {vehicle.model} ({vehicle.license_plate})
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.vehicle_id && <p className="text-sm text-red-600">{errors.vehicle_id}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="service_type">Type de service</Label>
                                <div className="grid gap-3">
                                    {services.map((service) => (
                                        <div
                                            key={service.value}
                                            className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                                                data.service_type === service.value
                                                    ? 'border-blue-500 bg-blue-50'
                                                    : 'border-gray-200 hover:border-gray-300'
                                            }`}
                                            onClick={() => setData('service_type', service.value)}
                                        >
                                            <div className="flex justify-between items-start">
                                                <div>
                                                    <h4 className="font-medium">{service.label}</h4>
                                                    <p className="text-sm text-gray-600">{service.description}</p>
                                                </div>
                                                <span className="text-lg font-bold text-green-600">
                                                    {service.price} DH
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                {errors.service_type && <p className="text-sm text-red-600">{errors.service_type}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="scheduled_at">Date et heure</Label>
                                <Input
                                    id="scheduled_at"
                                    type="datetime-local"
                                    value={data.scheduled_at}
                                    onChange={(e) => setData('scheduled_at', e.target.value)}
                                    min={minDate}
                                    required
                                />
                                <p className="text-xs text-gray-500">
                                    Horaires d'ouverture : 8h00 - 18h00, du lundi au samedi
                                </p>
                                {errors.scheduled_at && <p className="text-sm text-red-600">{errors.scheduled_at}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="notes">Notes (optionnel)</Label>
                                <Textarea
                                    id="notes"
                                    value={data.notes}
                                    onChange={(e) => setData('notes', e.target.value)}
                                    placeholder="Instructions spéciales, demandes particulières..."
                                    rows={3}
                                />
                                {errors.notes && <p className="text-sm text-red-600">{errors.notes}</p>}
                            </div>

                            <div className="flex gap-4">
                                <Button type="submit" disabled={processing} className="flex-1">
                                    {processing ? 'Modification...' : 'Modifier le rendez-vous'}
                                </Button>
                                <Button asChild variant="outline" type="button">
                                    <Link href={`/client/appointments/${appointment.id}`}>Annuler</Link>
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
