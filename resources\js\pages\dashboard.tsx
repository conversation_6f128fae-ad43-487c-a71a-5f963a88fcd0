import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type CalendarEvent } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Calendar, Clock, Plus, TrendingUp } from 'lucide-react';
import { useEffect, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard() {
    const [upcomingEvents, setUpcomingEvents] = useState<CalendarEvent[]>([]);
    const [todayEvents, setTodayEvents] = useState<CalendarEvent[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchEvents = async () => {
            try {
                const today = new Date();
                const nextWeek = new Date();
                nextWeek.setDate(today.getDate() + 7);

                const params = new URLSearchParams({
                    start: today.toISOString().split('T')[0],
                    end: nextWeek.toISOString().split('T')[0],
                });

                const response = await fetch(`/api/events?${params}`);
                const events = await response.json();

                const todayStr = today.toDateString();
                const todayEvts = events.filter((event: CalendarEvent) =>
                    new Date(event.start).toDateString() === todayStr
                );
                const upcomingEvts = events.filter((event: CalendarEvent) =>
                    new Date(event.start) > today
                ).slice(0, 5);

                setTodayEvents(todayEvts);
                setUpcomingEvents(upcomingEvts);
            } catch (error) {
                console.error('Failed to fetch events:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchEvents();
    }, []);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-4 overflow-x-auto">
                {/* Welcome Section */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Bienvenue sur SmartCalendar</h1>
                        <p className="text-muted-foreground">Gérez vos événements et planifiez votre temps efficacement</p>
                    </div>
                    <Button asChild>
                        <Link href="/events/create">
                            <Plus className="h-4 w-4 mr-2" />
                            Nouvel événement
                        </Link>
                    </Button>
                </div>

                {/* Stats Cards */}
                <div className="grid auto-rows-min gap-4 md:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Événements aujourd'hui</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{todayEvents.length}</div>
                            <p className="text-xs text-muted-foreground">
                                {todayEvents.length > 0 ? 'Journée chargée!' : 'Journée libre'}
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Événements à venir</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{upcomingEvents.length}</div>
                            <p className="text-xs text-muted-foreground">
                                Cette semaine
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Accès rapide</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <Button asChild variant="outline" className="w-full">
                                <Link href="/calendar">
                                    Voir le calendrier
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                </div>

                {/* Events Overview */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Today's Events */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Événements d'aujourd'hui</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {loading ? (
                                <div className="text-muted-foreground">Chargement...</div>
                            ) : todayEvents.length === 0 ? (
                                <div className="text-center py-8">
                                    <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <p className="text-muted-foreground">Aucun événement aujourd'hui</p>
                                    <p className="text-sm text-muted-foreground">Profitez de votre journée libre!</p>
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    {todayEvents.map((event) => (
                                        <div
                                            key={event.id}
                                            className="flex items-center space-x-3 p-3 rounded-lg border-l-4"
                                            style={{ borderLeftColor: event.backgroundColor }}
                                        >
                                            <div className="flex-1">
                                                <h4 className="font-medium">{event.title}</h4>
                                                <p className="text-sm text-muted-foreground">
                                                    {event.allDay ? 'Toute la journée' :
                                                        `${new Date(event.start).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })} - ${new Date(event.end).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`
                                                    }
                                                </p>
                                                {event.extendedProps.location && (
                                                    <p className="text-sm text-muted-foreground">📍 {event.extendedProps.location}</p>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Upcoming Events */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Événements à venir</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {loading ? (
                                <div className="text-muted-foreground">Chargement...</div>
                            ) : upcomingEvents.length === 0 ? (
                                <div className="text-center py-8">
                                    <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <p className="text-muted-foreground">Aucun événement à venir</p>
                                    <Button asChild className="mt-4">
                                        <Link href="/events/create">
                                            <Plus className="h-4 w-4 mr-2" />
                                            Planifier un événement
                                        </Link>
                                    </Button>
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    {upcomingEvents.map((event) => (
                                        <div
                                            key={event.id}
                                            className="flex items-center space-x-3 p-3 rounded-lg border-l-4"
                                            style={{ borderLeftColor: event.backgroundColor }}
                                        >
                                            <div className="flex-1">
                                                <h4 className="font-medium">{event.title}</h4>
                                                <p className="text-sm text-muted-foreground">
                                                    {new Date(event.start).toLocaleDateString('fr-FR', {
                                                        weekday: 'long',
                                                        day: 'numeric',
                                                        month: 'long'
                                                    })}
                                                </p>
                                                <p className="text-sm text-muted-foreground">
                                                    {event.allDay ? 'Toute la journée' :
                                                        new Date(event.start).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })
                                                    }
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Actions rapides</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Button asChild variant="outline" className="h-20 flex-col">
                                <Link href="/calendar">
                                    <Calendar className="h-6 w-6 mb-2" />
                                    Voir le calendrier
                                </Link>
                            </Button>
                            <Button asChild variant="outline" className="h-20 flex-col">
                                <Link href="/events/create">
                                    <Plus className="h-6 w-6 mb-2" />
                                    Nouvel événement
                                </Link>
                            </Button>
                            <Button asChild variant="outline" className="h-20 flex-col">
                                <Link href="/categories">
                                    <Calendar className="h-6 w-6 mb-2" />
                                    Gérer les catégories
                                </Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
