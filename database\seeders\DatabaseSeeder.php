<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'AutoWash',
            'email' => '<EMAIL>',
            'phone' => '0123456789',
            'role' => 'admin',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        // Create a test client
        User::create([
            'name' => 'Client Test',
            'email' => '<EMAIL>',
            'phone' => '0987654321',
            'role' => 'client',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        $this->call([
            CalendarSeeder::class,
        ]);
    }
}
