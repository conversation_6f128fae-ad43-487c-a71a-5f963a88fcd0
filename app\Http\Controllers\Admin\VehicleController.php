<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Inertia\Inertia;

class VehicleController extends Controller
{
    /**
     * Display a listing of vehicles.
     */
    public function index(Request $request)
    {
        $query = Vehicle::with('user')
            ->withCount('appointments');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('brand', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%")
                  ->orWhere('license_plate', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by vehicle type
        if ($request->filled('type')) {
            $query->where('vehicle_type', $request->type);
        }

        $vehicles = $query->latest()->paginate(15)->withQueryString();

        return Inertia::render('Admin/Vehicles/Index', [
            'vehicles' => $vehicles->toArray(), // Convert to array to ensure proper structure
            'filters' => $request->only(['search', 'type']),
        ]);
    }

    /**
     * Display the specified vehicle.
     */
    public function show(Vehicle $vehicle)
    {
        // Load relationships
        $vehicle->load([
            'user',
            'appointments'
        ]);

        // Calculate stats
        $stats = [
            'total_appointments' => $vehicle->appointments->count(),
            'completed_appointments' => $vehicle->appointments->where('status', 'completed')->count(),
            'pending_appointments' => $vehicle->appointments->where('status', 'pending')->count(),
            'total_revenue' => $vehicle->appointments->where('status', 'completed')->sum('price'),
            'last_appointment' => $vehicle->appointments->where('status', 'completed')->sortByDesc('scheduled_at')->first()?->scheduled_at,
        ];

        return Inertia::render('Admin/Vehicles/Show', [
            'vehicle' => $vehicle,
            'stats' => $stats,
        ]);
    }
}
