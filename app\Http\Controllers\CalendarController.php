<?php

namespace App\Http\Controllers;

use App\Models\Calendar;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class CalendarController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $calendars = Auth::user()->calendars()
            ->withCount('events')
            ->orderBy('is_default', 'desc')
            ->orderBy('name')
            ->get();

        return Inertia::render('Calendars/Index', [
            'calendars' => $calendars,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Calendars/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_default' => 'boolean',
            'is_public' => 'boolean',
        ]);

        // If this is set as default, unset other default calendars
        if ($validated['is_default'] ?? false) {
            Auth::user()->calendars()->update(['is_default' => false]);
        }

        $calendar = Auth::user()->calendars()->create($validated);

        return redirect()->route('calendars.index')
            ->with('success', 'Calendar created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Calendar $calendar)
    {
        $this->authorize('view', $calendar);

        $events = $calendar->events()
            ->with(['category'])
            ->orderBy('start_date')
            ->get();

        return Inertia::render('Calendars/Show', [
            'calendar' => $calendar,
            'events' => $events,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Calendar $calendar)
    {
        $this->authorize('update', $calendar);

        return Inertia::render('Calendars/Edit', [
            'calendar' => $calendar,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Calendar $calendar)
    {
        $this->authorize('update', $calendar);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_default' => 'boolean',
            'is_public' => 'boolean',
        ]);

        // If this is set as default, unset other default calendars
        if ($validated['is_default'] ?? false) {
            Auth::user()->calendars()->where('id', '!=', $calendar->id)->update(['is_default' => false]);
        }

        $calendar->update($validated);

        return redirect()->route('calendars.index')
            ->with('success', 'Calendar updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Calendar $calendar)
    {
        $this->authorize('delete', $calendar);

        $calendar->delete();

        return redirect()->route('calendars.index')
            ->with('success', 'Calendar deleted successfully.');
    }

    /**
     * Get calendars for API requests.
     */
    public function api()
    {
        return response()->json([
            'calendars' => Auth::user()->calendars()
                ->orderBy('is_default', 'desc')
                ->orderBy('name')
                ->get(['id', 'name', 'color', 'is_default'])
        ]);
    }
}
