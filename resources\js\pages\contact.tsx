import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle, MapPin, Phone, Mail, Clock } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';

type ContactForm = {
    name: string;
    email: string;
    phone: string;
    subject: string;
    message: string;
};

export default function Contact() {
    const { data, setData, post, processing, errors, reset } = useForm<Required<ContactForm>>({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('contact.store'), {
            onSuccess: () => {
                reset();
                // Show success message
            },
        });
    };

    return (
        <AppLayout>
            <Head title="Contact - AutoWash Pro" />
            
            <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 py-12">
                <div className="container mx-auto max-w-6xl px-4">
                    {/* Header */}
                    <div className="text-center mb-12">
                        <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                            Contactez-nous
                        </h1>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                            Une question ? Besoin d'informations ? Notre équipe est là pour vous aider.
                        </p>
                    </div>

                    <div className="grid lg:grid-cols-2 gap-12">
                        {/* Contact Form */}
                        <Card className="shadow-xl border-0">
                            <CardContent className="p-8">
                                <h2 className="text-2xl font-bold text-gray-900 mb-6">Envoyez-nous un message</h2>
                                
                                <form onSubmit={submit} className="space-y-6">
                                    <div className="grid md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="name">Nom complet *</Label>
                                            <Input
                                                id="name"
                                                type="text"
                                                required
                                                value={data.name}
                                                onChange={(e) => setData('name', e.target.value)}
                                                placeholder="Votre nom"
                                                className="mt-1"
                                            />
                                            <InputError message={errors.name} />
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="phone">Téléphone</Label>
                                            <Input
                                                id="phone"
                                                type="tel"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', e.target.value)}
                                                placeholder="Votre numéro"
                                                className="mt-1"
                                            />
                                            <InputError message={errors.phone} />
                                        </div>
                                    </div>

                                    <div>
                                        <Label htmlFor="email">Email *</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            required
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="<EMAIL>"
                                            className="mt-1"
                                        />
                                        <InputError message={errors.email} />
                                    </div>

                                    <div>
                                        <Label htmlFor="subject">Sujet *</Label>
                                        <Input
                                            id="subject"
                                            type="text"
                                            required
                                            value={data.subject}
                                            onChange={(e) => setData('subject', e.target.value)}
                                            placeholder="Objet de votre message"
                                            className="mt-1"
                                        />
                                        <InputError message={errors.subject} />
                                    </div>

                                    <div>
                                        <Label htmlFor="message">Message *</Label>
                                        <Textarea
                                            id="message"
                                            required
                                            value={data.message}
                                            onChange={(e) => setData('message', e.target.value)}
                                            placeholder="Décrivez votre demande..."
                                            rows={5}
                                            className="mt-1"
                                        />
                                        <InputError message={errors.message} />
                                    </div>

                                    <Button 
                                        type="submit" 
                                        className="w-full py-3 text-lg font-semibold text-white rounded-lg transition-all hover:opacity-90"
                                        style={{background: 'linear-gradient(135deg, #10B981 0%, #3B82F6 100%)'}}
                                        disabled={processing}
                                    >
                                        {processing && <LoaderCircle className="h-5 w-5 animate-spin mr-2" />}
                                        Envoyer le message
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>

                        {/* Contact Info */}
                        <div className="space-y-8">
                            <Card className="shadow-lg border-0">
                                <CardContent className="p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                            <MapPin className="w-6 h-6 text-white" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900 mb-2">Adresse</h3>
                                            <p className="text-gray-600">
                                                123 Avenue du Lavage<br />
                                                75001 Paris, France
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="shadow-lg border-0">
                                <CardContent className="p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                            <Phone className="w-6 h-6 text-white" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900 mb-2">Téléphone</h3>
                                            <p className="text-gray-600">
                                                +33 1 23 45 67 89<br />
                                                <span className="text-sm">Lun-Sam: 8h-19h</span>
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="shadow-lg border-0">
                                <CardContent className="p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                            <Mail className="w-6 h-6 text-white" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900 mb-2">Email</h3>
                                            <p className="text-gray-600">
                                                <EMAIL><br />
                                                <EMAIL>
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="shadow-lg border-0">
                                <CardContent className="p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                            <Clock className="w-6 h-6 text-white" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900 mb-2">Horaires</h3>
                                            <div className="text-gray-600 space-y-1">
                                                <p>Lundi - Vendredi: 8h00 - 19h00</p>
                                                <p>Samedi: 9h00 - 18h00</p>
                                                <p>Dimanche: 10h00 - 16h00</p>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
