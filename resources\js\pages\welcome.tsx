import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import {
    Calendar,
    Clock,
    Users,
    Bell,
    Search,
    BarChart3,
    Smartphone,
    Shield,
    Zap,
    CheckCircle,
    ArrowRight,
    Star
} from 'lucide-react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;

    return (
        <>
            <Head title="AutoWash Pro - Service de lavage automobile professionnel" />
            <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
                {/* Header */}
                <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
                    <div className="container mx-auto px-4 py-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <div className="w-8 h-8 bg-gradient-to-r from-green-600 to-blue-600 rounded-lg flex items-center justify-center">
                                    <Calendar className="w-5 h-5 text-white" />
                                </div>
                                <span className="text-xl font-bold text-gray-900">AutoWash Pro</span>
                            </div>

                            <nav className="hidden md:flex items-center space-x-8">
                                <a href="#services" className="text-gray-600 hover:text-green-600 transition-colors">Services</a>
                                <a href="#tarifs" className="text-gray-600 hover:text-green-600 transition-colors">Tarifs</a>
                                <a href="#contact" className="text-gray-600 hover:text-green-600 transition-colors">Contact</a>
                            </nav>

                            <div className="flex items-center space-x-4">
                                {auth.user ? (
                                    <Button asChild className="bg-green-600 hover:bg-green-700">
                                        <Link href={route('dashboard')}>
                                            Mon Compte
                                        </Link>
                                    </Button>
                                ) : (
                                    <Link
                                        href={route('login')}
                                        className="text-gray-600 hover:text-green-600 transition-colors font-medium"
                                    >
                                        Connexion
                                    </Link>
                                )}
                            </div>
                        </div>
                    </div>
                </header>
                {/* Hero Section */}
                <section className="py-20 px-4">
                    <div className="container mx-auto max-w-6xl">
                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div className="space-y-8">
                                <div className="space-y-4">
                                    <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                                        Votre voiture mérite
                                        <span className="text-green-600"> le meilleur</span>
                                        <span className="text-blue-600"> lavage</span>
                                    </h1>
                                    <p className="text-xl text-gray-600 leading-relaxed">
                                        Service de lavage automobile professionnel avec prise de rendez-vous en ligne.
                                        Nous prenons soin de votre véhicule comme s'il était le nôtre.
                                    </p>
                                </div>

                                <div className="flex flex-col sm:flex-row gap-4">
                                    <Button size="lg" className="text-lg px-8 py-4 bg-green-600 hover:bg-green-700" asChild>
                                        <Link href={route('register')}>
                                            Réserver maintenant
                                        </Link>
                                    </Button>
                                    <Button size="lg" variant="outline" className="text-lg px-8 py-4 border-green-600 text-green-600 hover:bg-green-50">
                                        Voir nos services
                                    </Button>
                                </div>
                            </div>

                            {/* Hero Illustration - Inspired by VoxiPlan */}
                            <div className="relative">
                                <div className="relative w-full max-w-lg mx-auto">
                                    {/* Central Circle */}
                                    <div className="w-64 h-64 mx-auto relative">
                                        <div className="absolute inset-0 bg-gradient-to-r from-green-100 to-blue-100 rounded-full opacity-20"></div>
                                        <div className="absolute inset-4 bg-gradient-to-r from-green-200 to-blue-200 rounded-full opacity-30"></div>
                                        <div className="absolute inset-8 bg-gradient-to-r from-green-300 to-blue-300 rounded-full opacity-40"></div>

                                        {/* Center Logo */}
                                        <div className="absolute inset-16 bg-gradient-to-r from-green-600 to-blue-600 rounded-full flex items-center justify-center">
                                            <Calendar className="w-16 h-16 text-white" />
                                        </div>

                                        {/* Floating Icons */}
                                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-green-200">
                                                <Clock className="w-6 h-6 text-green-500" />
                                            </div>
                                        </div>

                                        <div className="absolute top-8 -right-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-blue-200">
                                                <Bell className="w-6 h-6 text-blue-500" />
                                            </div>
                                        </div>

                                        <div className="absolute bottom-8 -right-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-teal-200">
                                                <Users className="w-6 h-6 text-teal-500" />
                                            </div>
                                        </div>

                                        <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-emerald-200">
                                                <BarChart3 className="w-6 h-6 text-emerald-500" />
                                            </div>
                                        </div>

                                        <div className="absolute bottom-8 -left-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-cyan-200">
                                                <Search className="w-6 h-6 text-cyan-500" />
                                            </div>
                                        </div>

                                        <div className="absolute top-8 -left-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-sky-200">
                                                <Smartphone className="w-6 h-6 text-sky-500" />
                                            </div>
                                        </div>

                                        {/* Dotted Connection Lines */}
                                        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 256 256">
                                            <circle cx="128" cy="32" r="2" fill="#059669" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" />
                                            </circle>
                                            <circle cx="200" cy="72" r="2" fill="#0284C7" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="0.3s" />
                                            </circle>
                                            <circle cx="200" cy="184" r="2" fill="#059669" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="0.6s" />
                                            </circle>
                                            <circle cx="128" cy="224" r="2" fill="#0284C7" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="0.9s" />
                                            </circle>
                                            <circle cx="56" cy="184" r="2" fill="#059669" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="1.2s" />
                                            </circle>
                                            <circle cx="56" cy="72" r="2" fill="#0284C7" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="1.5s" />
                                            </circle>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Services Section */}
                <section id="services" className="py-20 px-4 bg-white">
                    <div className="container mx-auto max-w-6xl">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                                Nos services de lavage automobile
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Des services professionnels adaptés à tous vos besoins, avec un système de réservation simple et efficace
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                                        <Calendar className="w-6 h-6 text-green-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Réservation en ligne</h3>
                                    <p className="text-gray-600">Réservez votre créneau de lavage en quelques clics, 24h/24 et 7j/7.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                                        <Users className="w-6 h-6 text-blue-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Équipe professionnelle</h3>
                                    <p className="text-gray-600">Nos experts prennent soin de votre véhicule avec des produits de qualité premium.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                                        <Bell className="w-6 h-6 text-teal-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Rappels automatiques</h3>
                                    <p className="text-gray-600">Recevez des notifications pour ne jamais oublier votre rendez-vous de lavage.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-sky-100 rounded-lg flex items-center justify-center mb-4">
                                        <Smartphone className="w-6 h-6 text-sky-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Service rapide</h3>
                                    <p className="text-gray-600">Lavage express ou complet, nous nous adaptons à votre emploi du temps.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mb-4">
                                        <BarChart3 className="w-6 h-6 text-emerald-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Suivi de qualité</h3>
                                    <p className="text-gray-600">Historique de vos lavages et évaluation de nos services pour une amélioration continue.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-cyan-100 rounded-lg flex items-center justify-center mb-4">
                                        <Shield className="w-6 h-6 text-cyan-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Produits écologiques</h3>
                                    <p className="text-gray-600">Nous utilisons des produits respectueux de l'environnement et de votre véhicule.</p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-20 px-4 bg-gradient-to-r from-green-600 to-blue-600">
                    <div className="container mx-auto max-w-4xl text-center">
                        <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                            Votre voiture mérite le meilleur traitement
                        </h2>
                        <p className="text-xl text-green-100 mb-8">
                            Rejoignez nos clients satisfaits et offrez à votre véhicule un lavage professionnel
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button size="lg" variant="secondary" className="text-lg px-8 py-4 bg-white text-green-600 hover:bg-gray-50" asChild>
                                <Link href={route('register')}>
                                    Réserver maintenant
                                    <ArrowRight className="ml-2 w-5 h-5" />
                                </Link>
                            </Button>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 text-white py-12 px-4">
                    <div className="container mx-auto max-w-6xl">
                        <div className="grid md:grid-cols-4 gap-8">
                            <div>
                                <div className="flex items-center space-x-2 mb-4">
                                    <div className="w-8 h-8 bg-gradient-to-r from-green-600 to-blue-600 rounded-lg flex items-center justify-center">
                                        <Calendar className="w-5 h-5 text-white" />
                                    </div>
                                    <span className="text-xl font-bold">AutoWash Pro</span>
                                </div>
                                <p className="text-gray-400">
                                    Votre service de lavage automobile professionnel avec réservation en ligne.
                                </p>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Services</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="#" className="hover:text-white transition-colors">Lavage extérieur</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Lavage complet</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Détailing</a></li>
                                </ul>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Support</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Horaires</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">FAQ</a></li>
                                </ul>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Entreprise</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="#" className="hover:text-white transition-colors">À propos</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Nos valeurs</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Emplois</a></li>
                                </ul>
                            </div>
                        </div>

                        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                            <p>&copy; 2024 AutoWash Pro. Tous droits réservés.</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
