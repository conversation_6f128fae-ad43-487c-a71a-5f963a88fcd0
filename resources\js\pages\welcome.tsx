import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import {
    Calendar,
    Clock,
    Users,
    Bell,
    Search,
    BarChart3,
    Smartphone,
    Shield,
    Zap,
    CheckCircle,
    ArrowRight,
    Star,
    Menu,
    X
} from 'lucide-react';
import { useState } from 'react';

export default function Welcome() {
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const { auth } = usePage<SharedData>().props;

    return (
        <>
            <Head title="AutoWash Pro - Service de lavage automobile professionnel" />
            <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
                {/* Header */}
                <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
                    <div className="container mx-auto px-4 py-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                    <Calendar className="w-5 h-5 text-white" />
                                </div>
                                <span className="text-xl font-bold text-gray-900">AutoWash Pro</span>
                            </div>

                            <nav className="hidden md:flex items-center">
                                <div className="flex items-center space-x-1 bg-gray-100 rounded-full p-1">
                                    <a href="#services" className="px-4 py-2 text-gray-600 hover:text-green-600 hover:bg-white rounded-full transition-all">Services</a>
                                    <a href="#tarifs" className="px-4 py-2 text-gray-600 hover:text-green-600 hover:bg-white rounded-full transition-all">Tarifs</a>
                                    <a href="#contact" className="px-4 py-2 text-gray-600 hover:text-green-600 hover:bg-white rounded-full transition-all">Contact</a>
                                </div>
                            </nav>

                            {/* Mobile menu button */}
                            <button
                                className="md:hidden p-2 text-gray-600 hover:text-green-600"
                                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                            >
                                {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
                            </button>

                            <div className="flex items-center space-x-4">
                                {auth.user ? (
                                    <Button asChild className="bg-green-600 hover:bg-green-700">
                                        <Link href={route('dashboard')}>
                                            Mon Compte
                                        </Link>
                                    </Button>
                                ) : (
                                    <div className="flex items-center space-x-4">
                                        <Link
                                            href={route('login')}
                                            className="text-gray-600 hover:text-green-600 transition-colors font-medium"
                                        >
                                            Connexion
                                        </Link>
                                        <Button asChild className="bg-blue-600 hover:bg-blue-700">
                                            <Link href={route('register')}>
                                                Inscription
                                            </Link>
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Mobile Menu */}
                    {mobileMenuOpen && (
                        <div className="md:hidden bg-white border-t border-gray-200">
                            <div className="px-4 py-4 space-y-4">
                                <a
                                    href="#services"
                                    className="block py-2 text-gray-600 hover:text-green-600 transition-colors"
                                    onClick={() => setMobileMenuOpen(false)}
                                >
                                    Services
                                </a>
                                <a
                                    href="#tarifs"
                                    className="block py-2 text-gray-600 hover:text-green-600 transition-colors"
                                    onClick={() => setMobileMenuOpen(false)}
                                >
                                    Tarifs
                                </a>
                                <a
                                    href="#contact"
                                    className="block py-2 text-gray-600 hover:text-green-600 transition-colors"
                                    onClick={() => setMobileMenuOpen(false)}
                                >
                                    Contact
                                </a>
                                <div className="pt-4 border-t border-gray-200 space-y-2">
                                    {user ? (
                                        <Button asChild className="w-full bg-green-600 hover:bg-green-700">
                                            <Link href={route('dashboard')}>
                                                Mon Compte
                                            </Link>
                                        </Button>
                                    ) : (
                                        <>
                                            <Link
                                                href={route('login')}
                                                className="block text-center py-2 text-gray-600 hover:text-green-600 transition-colors font-medium"
                                            >
                                                Connexion
                                            </Link>
                                            <Button asChild className="w-full bg-blue-600 hover:bg-blue-700">
                                                <Link href={route('register')}>
                                                    Inscription
                                                </Link>
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </header>
                {/* Hero Section */}
                <section className="py-20 px-4">
                    <div className="container mx-auto max-w-6xl">
                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div className="space-y-8">
                                <div className="space-y-4">
                                    <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                                        Votre voiture mérite
                                        <span className="text-green-600"> le meilleur</span>
                                        <span className="text-blue-600"> lavage</span>
                                    </h1>
                                    <p className="text-xl text-gray-600 leading-relaxed">
                                        Service de lavage automobile professionnel avec prise de rendez-vous en ligne.
                                        Nous prenons soin de votre véhicule comme s'il était le nôtre.
                                    </p>
                                </div>

                                <div className="flex flex-col sm:flex-row gap-4">
                                    <Button size="lg" className="text-lg px-8 py-4 bg-green-600 hover:bg-green-700" asChild>
                                        <Link href={route('register')}>
                                            Réserver maintenant
                                        </Link>
                                    </Button>
                                    <Button size="lg" variant="outline" className="text-lg px-8 py-4 border-blue-600 text-blue-600 hover:bg-blue-50">
                                        <a href="#services">Voir nos services</a>
                                    </Button>
                                </div>
                            </div>

                            {/* Hero Illustration - Inspired by VoxiPlan */}
                            <div className="relative">
                                <div className="relative w-full max-w-lg mx-auto">
                                    {/* Central Circle */}
                                    <div className="w-64 h-64 mx-auto relative">
                                        <div className="absolute inset-0 bg-gradient-to-r from-green-100 to-blue-100 rounded-full opacity-20"></div>
                                        <div className="absolute inset-4 bg-gradient-to-r from-green-200 to-blue-200 rounded-full opacity-30"></div>
                                        <div className="absolute inset-8 bg-gradient-to-r from-green-300 to-blue-300 rounded-full opacity-40"></div>

                                        {/* Center Logo */}
                                        <div className="absolute inset-16 bg-gradient-to-r from-green-600 to-blue-600 rounded-full flex items-center justify-center">
                                            <Calendar className="w-16 h-16 text-white" />
                                        </div>

                                        {/* Floating Icons */}
                                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-green-200">
                                                <Clock className="w-6 h-6 text-green-500" />
                                            </div>
                                        </div>

                                        <div className="absolute top-8 -right-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-blue-200">
                                                <Bell className="w-6 h-6 text-blue-500" />
                                            </div>
                                        </div>

                                        <div className="absolute bottom-8 -right-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-teal-200">
                                                <Users className="w-6 h-6 text-teal-500" />
                                            </div>
                                        </div>

                                        <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-emerald-200">
                                                <BarChart3 className="w-6 h-6 text-emerald-500" />
                                            </div>
                                        </div>

                                        <div className="absolute bottom-8 -left-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-cyan-200">
                                                <Search className="w-6 h-6 text-cyan-500" />
                                            </div>
                                        </div>

                                        <div className="absolute top-8 -left-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-sky-200">
                                                <Smartphone className="w-6 h-6 text-sky-500" />
                                            </div>
                                        </div>

                                        {/* Dotted Connection Lines */}
                                        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 256 256">
                                            <circle cx="128" cy="32" r="2" fill="#059669" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" />
                                            </circle>
                                            <circle cx="200" cy="72" r="2" fill="#0284C7" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="0.3s" />
                                            </circle>
                                            <circle cx="200" cy="184" r="2" fill="#059669" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="0.6s" />
                                            </circle>
                                            <circle cx="128" cy="224" r="2" fill="#0284C7" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="0.9s" />
                                            </circle>
                                            <circle cx="56" cy="184" r="2" fill="#059669" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="1.2s" />
                                            </circle>
                                            <circle cx="56" cy="72" r="2" fill="#0284C7" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="1.5s" />
                                            </circle>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Services Section */}
                <section id="services" className="py-20 px-4 bg-white">
                    <div className="container mx-auto max-w-6xl">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                                Nos services de lavage automobile
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Des services professionnels adaptés à tous vos besoins, avec un système de réservation simple et efficace
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                                        <Calendar className="w-6 h-6 text-green-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Réservation en ligne</h3>
                                    <p className="text-gray-600">Réservez votre créneau de lavage en quelques clics, 24h/24 et 7j/7.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                                        <Users className="w-6 h-6 text-blue-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Équipe professionnelle</h3>
                                    <p className="text-gray-600">Nos experts prennent soin de votre véhicule avec des produits de qualité premium.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                                        <Bell className="w-6 h-6 text-teal-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Rappels automatiques</h3>
                                    <p className="text-gray-600">Recevez des notifications pour ne jamais oublier votre rendez-vous de lavage.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-sky-100 rounded-lg flex items-center justify-center mb-4">
                                        <Smartphone className="w-6 h-6 text-sky-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Service rapide</h3>
                                    <p className="text-gray-600">Lavage express ou complet, nous nous adaptons à votre emploi du temps.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mb-4">
                                        <BarChart3 className="w-6 h-6 text-emerald-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Suivi de qualité</h3>
                                    <p className="text-gray-600">Historique de vos lavages et évaluation de nos services pour une amélioration continue.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-cyan-100 rounded-lg flex items-center justify-center mb-4">
                                        <Shield className="w-6 h-6 text-cyan-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Produits écologiques</h3>
                                    <p className="text-gray-600">Nous utilisons des produits respectueux de l'environnement et de votre véhicule.</p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>

                {/* Tarifs Section */}
                <section id="tarifs" className="py-20 px-4 bg-gray-50">
                    <div className="container mx-auto max-w-6xl">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                                Nos Tarifs
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Des prix transparents et compétitifs pour tous nos services de lavage automobile
                            </p>
                        </div>

                        <div className="grid md:grid-cols-3 gap-8">
                            {/* Lavage Basique */}
                            <Card className="relative p-8 hover:shadow-xl transition-shadow border-2 border-gray-200">
                                <CardContent className="text-center space-y-6">
                                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                                        <Sparkles className="w-8 h-8 text-green-600" />
                                    </div>
                                    <div>
                                        <h3 className="text-2xl font-bold text-gray-900 mb-2">Lavage Basique</h3>
                                        <div className="text-4xl font-bold text-green-600 mb-4">15€</div>
                                    </div>
                                    <ul className="space-y-3 text-left">
                                        <li className="flex items-center gap-3">
                                            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                                            <span className="text-gray-600">Lavage extérieur complet</span>
                                        </li>
                                        <li className="flex items-center gap-3">
                                            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                                            <span className="text-gray-600">Nettoyage des jantes</span>
                                        </li>
                                        <li className="flex items-center gap-3">
                                            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                                            <span className="text-gray-600">Séchage manuel</span>
                                        </li>
                                    </ul>
                                    <Button className="w-full bg-green-600 hover:bg-green-700" asChild>
                                        <Link href={route('register')}>
                                            Choisir ce forfait
                                        </Link>
                                    </Button>
                                </CardContent>
                            </Card>

                            {/* Lavage Premium */}
                            <Card className="relative p-8 hover:shadow-xl transition-shadow border-2 border-blue-500 bg-blue-50">
                                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                                        Populaire
                                    </span>
                                </div>
                                <CardContent className="text-center space-y-6">
                                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                                        <Shield className="w-8 h-8 text-blue-600" />
                                    </div>
                                    <div>
                                        <h3 className="text-2xl font-bold text-gray-900 mb-2">Lavage Premium</h3>
                                        <div className="text-4xl font-bold text-blue-600 mb-4">25€</div>
                                    </div>
                                    <ul className="space-y-3 text-left">
                                        <li className="flex items-center gap-3">
                                            <CheckCircle className="w-5 h-5 text-blue-600 flex-shrink-0" />
                                            <span className="text-gray-600">Tout du lavage basique</span>
                                        </li>
                                        <li className="flex items-center gap-3">
                                            <CheckCircle className="w-5 h-5 text-blue-600 flex-shrink-0" />
                                            <span className="text-gray-600">Nettoyage intérieur</span>
                                        </li>
                                        <li className="flex items-center gap-3">
                                            <CheckCircle className="w-5 h-5 text-blue-600 flex-shrink-0" />
                                            <span className="text-gray-600">Aspirateur complet</span>
                                        </li>
                                        <li className="flex items-center gap-3">
                                            <CheckCircle className="w-5 h-5 text-blue-600 flex-shrink-0" />
                                            <span className="text-gray-600">Nettoyage vitres</span>
                                        </li>
                                    </ul>
                                    <Button className="w-full bg-blue-600 hover:bg-blue-700" asChild>
                                        <Link href={route('register')}>
                                            Choisir ce forfait
                                        </Link>
                                    </Button>
                                </CardContent>
                            </Card>

                            {/* Lavage Deluxe */}
                            <Card className="relative p-8 hover:shadow-xl transition-shadow border-2 border-gray-200">
                                <CardContent className="text-center space-y-6">
                                    <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto">
                                        <Star className="w-8 h-8 text-yellow-600" />
                                    </div>
                                    <div>
                                        <h3 className="text-2xl font-bold text-gray-900 mb-2">Lavage Deluxe</h3>
                                        <div className="text-4xl font-bold text-yellow-600 mb-4">35€</div>
                                    </div>
                                    <ul className="space-y-3 text-left">
                                        <li className="flex items-center gap-3">
                                            <CheckCircle className="w-5 h-5 text-yellow-600 flex-shrink-0" />
                                            <span className="text-gray-600">Tout du lavage premium</span>
                                        </li>
                                        <li className="flex items-center gap-3">
                                            <CheckCircle className="w-5 h-5 text-yellow-600 flex-shrink-0" />
                                            <span className="text-gray-600">Cire de protection</span>
                                        </li>
                                        <li className="flex items-center gap-3">
                                            <CheckCircle className="w-5 h-5 text-yellow-600 flex-shrink-0" />
                                            <span className="text-gray-600">Lustrage carrosserie</span>
                                        </li>
                                        <li className="flex items-center gap-3">
                                            <CheckCircle className="w-5 h-5 text-yellow-600 flex-shrink-0" />
                                            <span className="text-gray-600">Traitement cuir/plastique</span>
                                        </li>
                                    </ul>
                                    <Button className="w-full bg-yellow-600 hover:bg-yellow-700" asChild>
                                        <Link href={route('register')}>
                                            Choisir ce forfait
                                        </Link>
                                    </Button>
                                </CardContent>
                            </Card>
                        </div>

                        <div className="text-center mt-12">
                            <p className="text-gray-600 mb-4">
                                Tous nos tarifs incluent les produits et la main d'œuvre professionnelle
                            </p>
                            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
                                <span className="flex items-center gap-2">
                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                    Produits écologiques
                                </span>
                                <span className="flex items-center gap-2">
                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                    Garantie satisfaction
                                </span>
                                <span className="flex items-center gap-2">
                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                    Paiement sécurisé
                                </span>
                            </div>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-20 px-4 bg-gradient-to-r from-green-500 to-blue-500">
                    <div className="container mx-auto max-w-4xl text-center">
                        <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                            Votre voiture mérite le meilleur traitement
                        </h2>
                        <p className="text-xl text-white/90 mb-8">
                            Rejoignez nos clients satisfaits et offrez à votre véhicule un lavage professionnel
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button size="lg" variant="secondary" className="text-lg px-8 py-4 bg-white text-green-600 hover:bg-gray-50" asChild>
                                <Link href={route('register')}>
                                    Réserver maintenant
                                    <ArrowRight className="ml-2 w-5 h-5" />
                                </Link>
                            </Button>
                            <Button size="lg" variant="outline" className="text-lg px-8 py-4 border-white text-white hover:bg-white/10">
                                <a href="#contact">
                                    Nous contacter
                                </a>
                            </Button>
                        </div>
                    </div>
                </section>

                {/* Contact Section */}
                <section id="contact" className="py-20 px-4 bg-gray-50">
                    <div className="container mx-auto max-w-6xl">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                                Contactez-nous
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Une question ? Besoin d'informations ? Notre équipe est là pour vous aider.
                            </p>
                        </div>

                        <div className="grid lg:grid-cols-2 gap-12">
                            {/* Contact Form */}
                            <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                                <h3 className="text-2xl font-bold text-gray-900 mb-6">Envoyez-nous un message</h3>

                                <form className="space-y-6">
                                    <div className="grid md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">Nom complet *</label>
                                            <input
                                                type="text"
                                                required
                                                placeholder="Votre nom"
                                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">Téléphone</label>
                                            <input
                                                type="tel"
                                                placeholder="Votre numéro"
                                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                            />
                                        </div>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                                        <input
                                            type="email"
                                            required
                                            placeholder="<EMAIL>"
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Sujet *</label>
                                        <input
                                            type="text"
                                            required
                                            placeholder="Objet de votre message"
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Message *</label>
                                        <textarea
                                            required
                                            placeholder="Décrivez votre demande..."
                                            rows={5}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                        ></textarea>
                                    </div>

                                    <button
                                        type="submit"
                                        className="w-full py-3 px-6 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-lg transition-colors"
                                    >
                                        Envoyer le message
                                    </button>
                                </form>
                            </div>

                            {/* Contact Info */}
                            <div className="space-y-8">
                                <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                                    <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-gray-900 mb-2">Adresse</h4>
                                            <p className="text-gray-600">
                                                123 Avenue du Lavage<br />
                                                75001 Paris, France
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                                    <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-gray-900 mb-2">Téléphone</h4>
                                            <p className="text-gray-600">
                                                +33 1 23 45 67 89<br />
                                                <span className="text-sm">Lun-Sam: 8h-19h</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                                    <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-gray-900 mb-2">Email</h4>
                                            <p className="text-gray-600">
                                                <EMAIL><br />
                                                <EMAIL>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                                    <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-gray-900 mb-2">Horaires</h4>
                                            <div className="text-gray-600 space-y-1">
                                                <p>Lundi - Vendredi: 8h00 - 19h00</p>
                                                <p>Samedi: 9h00 - 18h00</p>
                                                <p>Dimanche: 10h00 - 16h00</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 text-white py-12 px-4">
                    <div className="container mx-auto max-w-6xl">
                        <div className="grid md:grid-cols-4 gap-8">
                            <div>
                                <div className="flex items-center space-x-2 mb-4">
                                    <div className="w-8 h-8 bg-gradient-to-r from-green-600 to-blue-600 rounded-lg flex items-center justify-center">
                                        <Calendar className="w-5 h-5 text-white" />
                                    </div>
                                    <span className="text-xl font-bold">AutoWash Pro</span>
                                </div>
                                <p className="text-gray-400">
                                    Votre service de lavage automobile professionnel avec réservation en ligne.
                                </p>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Services</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="#" className="hover:text-white transition-colors">Lavage extérieur</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Lavage complet</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Détailing</a></li>
                                </ul>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Support</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Horaires</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">FAQ</a></li>
                                </ul>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Entreprise</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="#" className="hover:text-white transition-colors">À propos</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Nos valeurs</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Emplois</a></li>
                                </ul>
                            </div>
                        </div>

                        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                            <p>&copy; 2024 AutoWash Pro. Tous droits réservés.</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
