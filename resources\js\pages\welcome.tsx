import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import {
    Calendar,
    Clock,
    Users,
    Bell,
    Search,
    BarChart3,
    Smartphone,
    Shield,
    Zap,
    CheckCircle,
    ArrowRight,
    Star
} from 'lucide-react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;

    return (
        <>
            <Head title="SmartCalendar - La plateforme complète de gestion de calendrier" />
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
                {/* Header */}
                <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
                    <div className="container mx-auto px-4 py-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                                    <Calendar className="w-5 h-5 text-white" />
                                </div>
                                <span className="text-xl font-bold text-gray-900">SmartCalendar</span>
                            </div>

                            <nav className="hidden md:flex items-center space-x-8">
                                <a href="#features" className="text-gray-600 hover:text-blue-600 transition-colors">Fonctionnalités</a>
                                <a href="#pricing" className="text-gray-600 hover:text-blue-600 transition-colors">Tarifs</a>
                                <a href="#about" className="text-gray-600 hover:text-blue-600 transition-colors">À propos</a>
                            </nav>

                            <div className="flex items-center space-x-4">
                                {auth.user ? (
                                    <Button asChild>
                                        <Link href={route('dashboard')}>
                                            Dashboard
                                        </Link>
                                    </Button>
                                ) : (
                                    <>
                                        <Link
                                            href={route('login')}
                                            className="text-gray-600 hover:text-blue-600 transition-colors"
                                        >
                                            Connexion
                                        </Link>
                                        <Button asChild>
                                            <Link href={route('register')}>
                                                Essai gratuit
                                            </Link>
                                        </Button>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </header>
                {/* Hero Section */}
                <section className="py-20 px-4">
                    <div className="container mx-auto max-w-6xl">
                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div className="space-y-8">
                                <div className="space-y-4">
                                    <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                                        La plateforme complète
                                        <span className="text-blue-600"> d'automatisation</span> des
                                        <span className="text-indigo-600"> rendez-vous</span>
                                    </h1>
                                    <p className="text-xl text-gray-600 leading-relaxed">
                                        Planification en pilote automatique : Nous vous aidons à
                                        unifier les réservations par téléphone et sur le web.
                                    </p>
                                </div>

                                <div className="flex flex-col sm:flex-row gap-4">
                                    <Button size="lg" className="text-lg px-8 py-4" asChild>
                                        <Link href={route('register')}>
                                            Démarrer gratuitement
                                        </Link>
                                    </Button>
                                    <p className="text-sm text-gray-500 self-center">
                                        Essai gratuit de 14 jours · Aucune carte de crédit requise
                                    </p>
                                </div>
                            </div>

                            {/* Hero Illustration - Inspired by VoxiPlan */}
                            <div className="relative">
                                <div className="relative w-full max-w-lg mx-auto">
                                    {/* Central Circle */}
                                    <div className="w-64 h-64 mx-auto relative">
                                        <div className="absolute inset-0 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full opacity-20"></div>
                                        <div className="absolute inset-4 bg-gradient-to-r from-blue-200 to-indigo-200 rounded-full opacity-30"></div>
                                        <div className="absolute inset-8 bg-gradient-to-r from-blue-300 to-indigo-300 rounded-full opacity-40"></div>

                                        {/* Center Logo */}
                                        <div className="absolute inset-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
                                            <Calendar className="w-16 h-16 text-white" />
                                        </div>

                                        {/* Floating Icons */}
                                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-orange-200">
                                                <Clock className="w-6 h-6 text-orange-500" />
                                            </div>
                                        </div>

                                        <div className="absolute top-8 -right-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-blue-200">
                                                <Bell className="w-6 h-6 text-blue-500" />
                                            </div>
                                        </div>

                                        <div className="absolute bottom-8 -right-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-green-200">
                                                <Users className="w-6 h-6 text-green-500" />
                                            </div>
                                        </div>

                                        <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-purple-200">
                                                <BarChart3 className="w-6 h-6 text-purple-500" />
                                            </div>
                                        </div>

                                        <div className="absolute bottom-8 -left-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-red-200">
                                                <Search className="w-6 h-6 text-red-500" />
                                            </div>
                                        </div>

                                        <div className="absolute top-8 -left-8">
                                            <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-indigo-200">
                                                <Smartphone className="w-6 h-6 text-indigo-500" />
                                            </div>
                                        </div>

                                        {/* Dotted Connection Lines */}
                                        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 256 256">
                                            <circle cx="128" cy="32" r="2" fill="#3B82F6" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" />
                                            </circle>
                                            <circle cx="200" cy="72" r="2" fill="#3B82F6" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="0.3s" />
                                            </circle>
                                            <circle cx="200" cy="184" r="2" fill="#3B82F6" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="0.6s" />
                                            </circle>
                                            <circle cx="128" cy="224" r="2" fill="#3B82F6" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="0.9s" />
                                            </circle>
                                            <circle cx="56" cy="184" r="2" fill="#3B82F6" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="1.2s" />
                                            </circle>
                                            <circle cx="56" cy="72" r="2" fill="#3B82F6" opacity="0.3">
                                                <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite" begin="1.5s" />
                                            </circle>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section id="features" className="py-20 px-4 bg-white">
                    <div className="container mx-auto max-w-6xl">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                                Fonctionnalités puissantes pour votre entreprise
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Découvrez comment SmartCalendar peut transformer votre gestion de rendez-vous
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                                        <Calendar className="w-6 h-6 text-blue-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Planification automatique</h3>
                                    <p className="text-gray-600">Automatisez vos rendez-vous avec notre IA intelligente qui optimise votre planning.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                                        <Users className="w-6 h-6 text-green-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Gestion multi-utilisateurs</h3>
                                    <p className="text-gray-600">Gérez facilement les calendriers de toute votre équipe depuis une interface unique.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                                        <Bell className="w-6 h-6 text-purple-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Notifications intelligentes</h3>
                                    <p className="text-gray-600">Recevez des rappels personnalisés et ne manquez plus jamais un rendez-vous important.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                                        <Smartphone className="w-6 h-6 text-orange-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Application mobile</h3>
                                    <p className="text-gray-600">Accédez à vos calendriers partout, à tout moment avec notre app mobile native.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                                        <BarChart3 className="w-6 h-6 text-red-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Analyses avancées</h3>
                                    <p className="text-gray-600">Obtenez des insights détaillés sur vos performances et optimisez votre productivité.</p>
                                </CardContent>
                            </Card>

                            <Card className="p-6 hover:shadow-lg transition-shadow">
                                <CardContent className="p-0">
                                    <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                                        <Shield className="w-6 h-6 text-indigo-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Sécurité renforcée</h3>
                                    <p className="text-gray-600">Vos données sont protégées par un chiffrement de niveau entreprise.</p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-indigo-600">
                    <div className="container mx-auto max-w-4xl text-center">
                        <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                            Prêt à transformer votre gestion de calendrier ?
                        </h2>
                        <p className="text-xl text-blue-100 mb-8">
                            Rejoignez des milliers d'entreprises qui font confiance à SmartCalendar
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button size="lg" variant="secondary" className="text-lg px-8 py-4" asChild>
                                <Link href={route('register')}>
                                    Commencer gratuitement
                                    <ArrowRight className="ml-2 w-5 h-5" />
                                </Link>
                            </Button>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 text-white py-12 px-4">
                    <div className="container mx-auto max-w-6xl">
                        <div className="grid md:grid-cols-4 gap-8">
                            <div>
                                <div className="flex items-center space-x-2 mb-4">
                                    <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                                        <Calendar className="w-5 h-5 text-white" />
                                    </div>
                                    <span className="text-xl font-bold">SmartCalendar</span>
                                </div>
                                <p className="text-gray-400">
                                    La solution complète pour automatiser et optimiser vos rendez-vous.
                                </p>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Produit</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="#" className="hover:text-white transition-colors">Fonctionnalités</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Tarifs</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">API</a></li>
                                </ul>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Support</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">FAQ</a></li>
                                </ul>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Entreprise</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="#" className="hover:text-white transition-colors">À propos</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                                    <li><a href="#" className="hover:text-white transition-colors">Carrières</a></li>
                                </ul>
                            </div>
                        </div>

                        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                            <p>&copy; 2024 SmartCalendar. Tous droits réservés.</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
